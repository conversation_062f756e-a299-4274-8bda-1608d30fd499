import { describe, it, expect, beforeAll, afterAll, beforeEach } from '@jest/globals';
import { 
  prisma, 
  setupTestDatabase, 
  createTestUser, 
  createTestWebhook, 
  createTestDomain 
} from '../../setup/test-db-setup.js';

describe('Webhook Synchronization Integration', () => {
  setupTestDatabase();

  let testUser: any;
  let testDomain: any;
  let testWebhook1: any;
  let testWebhook2: any;
  let catchAllAlias: any;

  beforeEach(async () => {
    // Create test user
    testUser = await createTestUser({
      email: '<EMAIL>',
      password: 'testpassword123'
    });
    
    // Create test webhooks
    testWebhook1 = await createTestWebhook(testUser.id, {
      name: 'Test Webhook 1',
      url: 'https://test1.example.com/webhook',
      verified: true
    });

    testWebhook2 = await createTestWebhook(testUser.id, {
      name: 'Test Webhook 2', 
      url: 'https://test2.example.com/webhook',
      verified: true
    });
    
    // Create test domain
    testDomain = await createTestDomain(testUser.id, testWebhook1.id, {
      domain: 'test-webhook-sync.com',
      verified: true
    });

    // Create catch-all alias
    catchAllAlias = await prisma.alias.create({
      data: {
        email: `*@${testDomain.domain}`,
        domainId: testDomain.id,
        webhookId: testWebhook1.id,
        active: true
      }
    });
  });

  describe('Webhook Sync Service', () => {
    it('should create webhook sync relationship', async () => {
      const { WebhookSyncService } = await import('../../../src/backend/services/user/webhook-sync.service.js');
      const syncService = new WebhookSyncService();

      const result = await syncService.createSync({
        domainId: testDomain.id,
        catchAllAliasId: catchAllAlias.id,
        webhookId: testWebhook1.id,
        syncEnabled: true
      });

      expect(result.success).toBe(true);
      expect(result.syncId).toBeDefined();

      // Verify sync relationship was created in database
      const syncRecord = await prisma.webhookSync.findUnique({
        where: {
          domainId_catchAllAliasId: {
            domainId: testDomain.id,
            catchAllAliasId: catchAllAlias.id
          }
        }
      });

      expect(syncRecord).toBeTruthy();
      expect(syncRecord!.webhookId).toBe(testWebhook1.id);
      expect(syncRecord!.syncEnabled).toBe(true);
    });

    it('should sync domain webhook to catch-all alias', async () => {
      const { WebhookSyncService } = await import('../../../src/backend/services/user/webhook-sync.service.js');
      const syncService = new WebhookSyncService();

      // Create sync relationship
      await syncService.createSync({
        domainId: testDomain.id,
        catchAllAliasId: catchAllAlias.id,
        webhookId: testWebhook1.id,
        syncEnabled: true
      });

      // Update domain webhook and trigger sync
      const result = await syncService.syncDomainWebhookToAlias(testDomain.id, testWebhook2.id);

      expect(result.success).toBe(true);

      // Verify alias webhook was updated
      const updatedAlias = await prisma.alias.findUnique({
        where: { id: catchAllAlias.id }
      });

      expect(updatedAlias!.webhookId).toBe(testWebhook2.id);

      // Verify sync relationship was updated
      const syncRecord = await prisma.webhookSync.findUnique({
        where: {
          domainId_catchAllAliasId: {
            domainId: testDomain.id,
            catchAllAliasId: catchAllAlias.id
          }
        }
      });

      expect(syncRecord!.webhookId).toBe(testWebhook2.id);
    });

    it('should sync catch-all alias webhook to domain', async () => {
      const { WebhookSyncService } = await import('../../../src/backend/services/user/webhook-sync.service.js');
      const syncService = new WebhookSyncService();

      // Create sync relationship
      await syncService.createSync({
        domainId: testDomain.id,
        catchAllAliasId: catchAllAlias.id,
        webhookId: testWebhook1.id,
        syncEnabled: true
      });

      // Update alias webhook and trigger sync
      const result = await syncService.syncAliasWebhookToDomain(catchAllAlias.id, testWebhook2.id);

      expect(result.success).toBe(true);

      // Verify domain webhook was updated
      const updatedDomain = await prisma.domain.findUnique({
        where: { id: testDomain.id }
      });

      expect(updatedDomain!.webhookId).toBe(testWebhook2.id);

      // Verify sync relationship was updated
      const syncRecord = await prisma.webhookSync.findUnique({
        where: {
          domainId_catchAllAliasId: {
            domainId: testDomain.id,
            catchAllAliasId: catchAllAlias.id
          }
        }
      });

      expect(syncRecord!.webhookId).toBe(testWebhook2.id);
    });

    it('should disable/enable webhook synchronization', async () => {
      const { WebhookSyncService } = await import('../../../src/backend/services/user/webhook-sync.service.js');
      const syncService = new WebhookSyncService();

      // Create sync relationship
      await syncService.createSync({
        domainId: testDomain.id,
        catchAllAliasId: catchAllAlias.id,
        webhookId: testWebhook1.id,
        syncEnabled: true
      });

      // Disable sync
      const disableResult = await syncService.toggleSync(testDomain.id, catchAllAlias.id, false);
      expect(disableResult.success).toBe(true);

      // Verify sync is disabled
      let syncRecord = await prisma.webhookSync.findUnique({
        where: {
          domainId_catchAllAliasId: {
            domainId: testDomain.id,
            catchAllAliasId: catchAllAlias.id
          }
        }
      });
      expect(syncRecord!.syncEnabled).toBe(false);

      // Try to sync - should not work when disabled
      await syncService.syncDomainWebhookToAlias(testDomain.id, testWebhook2.id);
      
      const aliasAfterDisabledSync = await prisma.alias.findUnique({
        where: { id: catchAllAlias.id }
      });
      expect(aliasAfterDisabledSync!.webhookId).toBe(testWebhook1.id); // Should not have changed

      // Enable sync
      const enableResult = await syncService.toggleSync(testDomain.id, catchAllAlias.id, true);
      expect(enableResult.success).toBe(true);

      // Verify sync is enabled
      syncRecord = await prisma.webhookSync.findUnique({
        where: {
          domainId_catchAllAliasId: {
            domainId: testDomain.id,
            catchAllAliasId: catchAllAlias.id
          }
        }
      });
      expect(syncRecord!.syncEnabled).toBe(true);
    });

    it('should remove webhook sync relationship', async () => {
      const { WebhookSyncService } = await import('../../../src/backend/services/user/webhook-sync.service.js');
      const syncService = new WebhookSyncService();

      // Create sync relationship
      await syncService.createSync({
        domainId: testDomain.id,
        catchAllAliasId: catchAllAlias.id,
        webhookId: testWebhook1.id,
        syncEnabled: true
      });

      // Remove sync
      const result = await syncService.removeSync(testDomain.id, catchAllAlias.id);
      expect(result.success).toBe(true);

      // Verify sync relationship was removed
      const syncRecord = await prisma.webhookSync.findUnique({
        where: {
          domainId_catchAllAliasId: {
            domainId: testDomain.id,
            catchAllAliasId: catchAllAlias.id
          }
        }
      });

      expect(syncRecord).toBeNull();
    });
  });

  describe('Domain Service Integration', () => {
    it('should trigger webhook sync when domain webhook is updated', async () => {
      const { DomainService } = await import('../../../src/backend/services/user/domain.service.js');
      const { WebhookSyncService } = await import('../../../src/backend/services/user/webhook-sync.service.js');
      
      const domainService = new DomainService();
      const syncService = new WebhookSyncService();

      // Create sync relationship
      await syncService.createSync({
        domainId: testDomain.id,
        catchAllAliasId: catchAllAlias.id,
        webhookId: testWebhook1.id,
        syncEnabled: true
      });

      // Update domain webhook through service
      const result = await domainService.updateDomainWebhook(testDomain.id, testUser.id, testWebhook2.id);

      expect(result.success).toBe(true);

      // Verify alias webhook was automatically synced
      const updatedAlias = await prisma.alias.findUnique({
        where: { id: catchAllAlias.id }
      });

      expect(updatedAlias!.webhookId).toBe(testWebhook2.id);
    });
  });

  describe('Alias Service Integration', () => {
    it('should trigger webhook sync when catch-all alias webhook is updated', async () => {
      const { AliasService } = await import('../../../src/backend/services/user/alias.service.js');
      const { WebhookSyncService } = await import('../../../src/backend/services/user/webhook-sync.service.js');
      
      const aliasService = new AliasService();
      const syncService = new WebhookSyncService();

      // Create sync relationship
      await syncService.createSync({
        domainId: testDomain.id,
        catchAllAliasId: catchAllAlias.id,
        webhookId: testWebhook1.id,
        syncEnabled: true
      });

      // Update alias webhook through service
      const result = await aliasService.updateAliasWebhook(catchAllAlias.id, testUser.id, testWebhook2.id);

      expect(result.success).toBe(true);

      // Verify domain webhook was automatically synced
      const updatedDomain = await prisma.domain.findUnique({
        where: { id: testDomain.id }
      });

      expect(updatedDomain!.webhookId).toBe(testWebhook2.id);
    });

    it('should not trigger sync for non-catch-all aliases', async () => {
      const { AliasService } = await import('../../../src/backend/services/user/alias.service.js');
      const { WebhookSyncService } = await import('../../../src/backend/services/user/webhook-sync.service.js');
      
      const aliasService = new AliasService();
      const syncService = new WebhookSyncService();

      // Create specific alias (not catch-all)
      const specificAlias = await prisma.alias.create({
        data: {
          email: `support@${testDomain.domain}`,
          domainId: testDomain.id,
          webhookId: testWebhook1.id,
          active: true
        }
      });

      // Create sync relationship for catch-all (not specific alias)
      await syncService.createSync({
        domainId: testDomain.id,
        catchAllAliasId: catchAllAlias.id,
        webhookId: testWebhook1.id,
        syncEnabled: true
      });

      // Update specific alias webhook
      const result = await aliasService.updateAliasWebhook(specificAlias.id, testUser.id, testWebhook2.id);

      expect(result.success).toBe(true);

      // Verify domain webhook was NOT synced (should still be testWebhook1)
      const domainAfterUpdate = await prisma.domain.findUnique({
        where: { id: testDomain.id }
      });

      expect(domainAfterUpdate!.webhookId).toBe(testWebhook1.id); // Should not have changed
    });
  });
});
