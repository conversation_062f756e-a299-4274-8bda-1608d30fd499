import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import tailwindcss from '@tailwindcss/vite'
import { resolve } from 'path'

export default defineConfig({
  plugins: [vue(), tailwindcss()],
  css: {
    postcss: './postcss.config.js',
  },
  esbuild: {
    tsconfigRaw: {
      compilerOptions: {
        baseUrl: '.',
        paths: {
          '@/*': ['./src/frontend/*'],
          '@components/*': ['./src/frontend/components/*'],
          '@composables/*': ['./src/frontend/composables/*'],
          '@stores/*': ['./src/frontend/stores/*'],
          '@types': ['./src/frontend/types/index.ts'],
          '@types/*': ['./src/frontend/types/*'],
          '@utils/*': ['./src/frontend/utils/*']
        }
      }
    }
  },
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src/frontend'),
      '@components': resolve(__dirname, 'src/frontend/components'),
      '@composables': resolve(__dirname, 'src/frontend/composables'),
      '@stores': resolve(__dirname, 'src/frontend/stores'),
      '@types': resolve(__dirname, 'src/frontend/types'),
      '@utils': resolve(__dirname, 'src/frontend/utils')
    }
  },
  build: {
    outDir: 'dist/frontend',
    emptyOutDir: true,
    manifest: true, // Generate manifest.json for asset paths
    rollupOptions: {
      input: resolve(__dirname, 'index.html'),
      output: {
        // Simplified manual chunking strategy to avoid circular dependencies
        manualChunks(id) {
          // Node modules - vendor chunks
          if (id.includes('node_modules')) {
            // Core Vue libraries
            if (id.includes('vue') || id.includes('pinia')) {
              return 'vendor-core'
            }
            // Other vendor libraries
            return 'vendor-libs'
          }
          
          // Application code chunking
          if (id.includes('/src/frontend/')) {
            // Dashboard components
            if (id.includes('/components/dashboard/')) {
              return 'dashboard'
            }
            
            // Auth components  
            if (id.includes('/components/auth/')) {
              return 'auth'
            }
            
            // Landing and legal pages
            if (id.includes('/components/landing/') || id.includes('/components/legal/')) {
              return 'public'
            }
            
            // Settings
            if (id.includes('/components/settings/')) {
              return 'settings'
            }
            
            // UI components and shared utilities
            if (id.includes('/components/ui/') || id.includes('/composables/') || id.includes('/utils/')) {
              return 'shared'
            }
          }
          
          // Everything else goes to the main chunk
          return undefined
        },
        
        // Optimize chunk naming
        chunkFileNames: (chunkInfo) => {
          const facadeModuleId = chunkInfo.facadeModuleId
          if (facadeModuleId) {
            const fileName = facadeModuleId.split('/').pop()?.replace('.vue', '') || 'chunk'
            return `chunks/${fileName}-[hash].js`
          }
          return 'chunks/[name]-[hash].js'
        },
        
        // Optimize asset naming
        assetFileNames: (assetInfo) => {
          const name = assetInfo.name || ''
          
          // Group assets by type
          if (name.endsWith('.css')) {
            return 'assets/css/[name]-[hash][extname]'
          }
          
          if (/\.(png|jpe?g|svg|gif|webp|avif)$/i.test(name)) {
            return 'assets/images/[name]-[hash][extname]'  
          }
          
          if (/\.(woff2?|eot|ttf|otf)$/i.test(name)) {
            return 'assets/fonts/[name]-[hash][extname]'
          }
          
          return 'assets/[name]-[hash][extname]'
        }
      }
    },
    assetsInlineLimit: 0, // Don't inline any assets, always copy them
    
    // Increase chunk size warning limit to 600kb (reasonable for main chunks)
    chunkSizeWarningLimit: 600,
    
    // Enable minification and optimization
    minify: 'esbuild', // Fast and effective
    target: 'es2020', // Modern target for better optimization
    
    // Source maps for debugging (disable in production if not needed)
    sourcemap: false
  },
  publicDir: 'public',
  server: {
    port: 5173,
    proxy: {
      '/api': {
        target: 'http://localhost:3000',
        changeOrigin: true
      },
      '/admin': {
        target: 'http://localhost:3000',
        changeOrigin: true
      }
    }
  }
})
