// Shared types for payment system

export interface CreatePaymentRequest {
  planType: 'pro' | 'enterprise';
  interval: 'monthly' | 'yearly';
  successUrl?: string;
  cancelUrl?: string;
}

export interface CreatePaymentResponse {
  success: boolean;
  paymentId: string;
  checkoutUrl: string;
  amount: {
    value: string;
    currency: string;
  };
}

export interface PaymentWebhookData {
  id: string; // Mollie payment ID
  status: string;
  amount: {
    value: string;
    currency: string;
  };
  description: string;
  method?: string;
  paidAt?: string;
  cancelledAt?: string;
  expiredAt?: string;
  failedAt?: string;
  metadata?: Record<string, any>;
}

export interface SubscriptionWebhookData {
  id: string; // Mollie subscription ID
  customerId: string;
  status: string;
  amount: {
    value: string;
    currency: string;
  };
  interval: string;
  description: string;
  createdAt: string;
  nextPaymentDate?: string;
  cancelledAt?: string;
  metadata?: Record<string, any>;
}

export interface BillingInfo {
  currentPlan: {
    type: string;
    name: string;
    interval?: string;
    amount?: {
      value: string;
      currency: string;
    };
    nextPaymentDate?: string;
    status?: string;
  };
  paymentMethods: PaymentMethodInfo[];
  recentPayments: PaymentInfo[];
  usage: {
    emails: number;
    domains: number;
    webhooks: number;
    aliases: number;
  };
  limits: {
    emails: number;
    domains: number;
    webhooks: number;
    aliases: number;
  };
  // Enhanced billing data
  emailBreakdown?: {
    monthlyAllowance: number;
    monthlyUsed: number;
    monthlyRemaining: number;
    purchasedCredits: number;
    totalAvailable: number;
  };
  creditInfo?: {
    balance: number;
    expiringCredits: number;
    nextExpirationDate: string | null;
  };
}

export interface PaymentMethodInfo {
  id: string;
  type: string;
  description: string;
  isDefault: boolean;
  cardHolder?: string;
  cardNumber?: string; // Last 4 digits
  cardExpiryDate?: string;
  createdAt: string;
}

export interface PaymentInfo {
  id: string;
  amount: {
    value: string;
    currency: string;
  };
  description: string;
  status: string;
  method?: string;
  paidAt?: string;
  createdAt: string;
}

export interface SubscriptionInfo {
  id: string;
  planType: string;
  interval: string;
  amount: {
    value: string;
    currency: string;
  };
  status: string;
  startDate?: string;
  nextPaymentDate?: string;
  cancelledAt?: string;
  cancelReason?: string;
}

// Plan configuration types
export interface PlanPricing {
  monthly: number;
  yearly: number;
  currency: string;
}

export interface PlanFeatures {
  monthlyEmailLimit: number;
  domains?: number;
  webhooks?: number;
  aliases?: number;
  features: string[];
}

export interface PlanInfo extends PlanFeatures {
  id: string;
  name: string;
  description?: string;
  price?: PlanPricing;
  popular?: boolean;
}

// API response types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// Webhook event types
export type WebhookEventType = 
  | 'payment.paid'
  | 'payment.failed'
  | 'payment.expired'
  | 'payment.cancelled'
  | 'subscription.created'
  | 'subscription.activated'
  | 'subscription.suspended'
  | 'subscription.cancelled'
  | 'subscription.completed';

export interface WebhookEvent {
  type: WebhookEventType;
  data: PaymentWebhookData | SubscriptionWebhookData;
  timestamp: string;
}
