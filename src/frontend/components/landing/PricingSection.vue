<template>
  <div id="pricing" class="bg-base-200 py-20">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <!-- Section Header -->
      <div class="text-center mb-16">
        <div class="badge badge-primary mb-4">Pricing</div>
        <h2 class="text-3xl lg:text-4xl font-bold text-base-content mb-4">
          Simple, transparent pricing
        </h2>
        <p class="text-xl text-base-content/70 max-w-3xl mx-auto">
          Start free and scale as you grow. No hidden fees, no setup costs.
        </p>
      </div>
      
      <!-- Pricing Cards -->
      <div class="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-16">
        <!-- Starter Plan -->
        <div class="card bg-base-100 shadow-lg h-full">
          <div class="card-body flex flex-col h-full">
            <div class="text-center">
              <h3 class="card-title text-2xl justify-center mb-2">Starter</h3>
              <p class="text-base-content/70 mb-6">Perfect for small projects and testing</p>
              <div class="mb-6">
                <span class="text-4xl font-bold">€0</span>
                <span class="text-base-content/70">/month</span>
              </div>
            </div>
            
            <div class="space-y-3 mb-6 flex-grow">
              <div class="flex items-center space-x-3">
                <svg class="w-5 h-5 text-success" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                </svg>
                <span>50 emails/month</span>
              </div>
              <div class="flex items-center space-x-3">
                <svg class="w-5 h-5 text-success" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                </svg>
                <span>3 domains, 10 aliases & webhooks</span>
              </div>
              <div class="flex items-center space-x-3">
                <svg class="w-5 h-5 text-success" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                </svg>
                <span>Basic webhook delivery</span>
              </div>
              <div class="flex items-center space-x-3">
                <svg class="w-5 h-5 text-success" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                </svg>
                <span>Limited support</span>
              </div>
              <div class="flex items-center space-x-3">
                <svg class="w-5 h-5 text-success" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                </svg>
                <span>30-day data retention</span>
              </div>
            </div>
            
            <div class="card-actions justify-center mt-auto">
              <router-link to="/register" class="btn btn-primary btn-block">Get started free</router-link>
            </div>
          </div>
        </div>
        
        <!-- Professional Plan -->
        <div class="card bg-base-100 shadow-xl border-2 border-primary relative h-full">
          <!-- <div class="absolute -top-4 left-1/2 transform -translate-x-1/2">
            <div class="badge badge-primary badge-lg">Most Popular</div>
          </div> -->
          <div class="card-body flex flex-col h-full">
            <div class="text-center">
              <h3 class="card-title text-2xl justify-center mb-2">Professional</h3>
              <p class="text-base-content/70 mb-6">For growing businesses and applications</p>
              <div class="mb-6">
                <span class="text-4xl font-bold">€29</span>
                <span class="text-base-content/70">/month</span>
              </div>
            </div>
            
            <div class="space-y-3 mb-6 flex-grow">
              <div class="flex items-center space-x-3">
                <svg class="w-5 h-5 text-success" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                </svg>
                <span>5,000 emails/month</span>
              </div>
              <div class="flex items-center space-x-3">
                <svg class="w-5 h-5 text-success" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                </svg>
                <span>Unlimited domains, aliases & webhooks</span>
              </div>
              <div class="flex items-center space-x-3">
                <svg class="w-5 h-5 text-success" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                </svg>
                <span>Priority queue & delivery</span>
              </div>
              <div class="flex items-center space-x-3">
                <svg class="w-5 h-5 text-success" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                </svg>
                <span>Priority support</span>
              </div>
              <div class="flex items-center space-x-3">
                <svg class="w-5 h-5 text-success" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                </svg>
                <span>90-day data retention</span>
              </div>
            </div>
            
            <div class="card-actions justify-center mt-auto">
              <router-link to="/register" class="btn btn-primary btn-block">Start professional</router-link>
            </div>
          </div>
        </div>
        
        <!-- Enterprise Plan -->
        <div class="card bg-base-100 shadow-lg h-full">
          <div class="card-body flex flex-col h-full">
            <div class="text-center">
              <h3 class="card-title text-2xl justify-center mb-2">Enterprise</h3>
              <p class="text-base-content/70 mb-6">For large-scale applications</p>
              <div class="mb-6">
                <span class="text-2xl font-bold">Custom</span>
              </div>
            </div>
            
            <div class="text-center mb-6 flex-grow">
              <p class="text-base-content/70">
                Contact me to discuss your specific requirements for volume, 
                priority, SLA guarantees, and custom integrations.
              </p>
            </div>
            
            <div class="card-actions justify-center mt-auto">
              <a href="mailto:<EMAIL>" class="btn btn-primary btn-block">Contact sales</a>
            </div>
          </div>
        </div>
      </div>
      
      <!-- FAQ Section -->
      <div class="bg-base-100 rounded-2xl p-8 lg:p-12">
        <h3 class="text-2xl font-bold text-center mb-8">Frequently Asked Questions</h3>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
          <div>
            <h4 class="font-semibold mb-2">What happens if I exceed my email limit?</h4>
            <p class="text-base-content/70 text-sm">
              Once you hit 80% of your limit, we'll notify you via email. If you exceed your limit, 
              new emails won't be processed until you upgrade or the next billing cycle.
            </p>
          </div>
          <div>
            <h4 class="font-semibold mb-2">Can I change plans anytime?</h4>
            <p class="text-base-content/70 text-sm">
              Yes, you can upgrade or downgrade your plan at any time. Changes take effect immediately with prorated billing.
            </p>
          </div>
          <div>
            <h4 class="font-semibold mb-2">Is there a setup fee?</h4>
            <p class="text-base-content/70 text-sm">
              No setup fees, no hidden costs. You only pay the monthly subscription fee for your chosen plan.
            </p>
          </div>
          <div>
            <h4 class="font-semibold mb-2">What about GDPR compliance?</h4>
            <p class="text-base-content/70 text-sm">
              All data is processed on EU servers with EU-operated infrastructure (no Cloud Act, no Patriot Act). 
              Automatic expiration, audit logging, and all 3rd party providers are EU-based.
            </p>
          </div>
        </div>
      </div>
      
      <!-- Bottom CTA -->
      <div class="text-center mt-12">
        <div class="bg-gradient-to-r from-primary to-secondary rounded-2xl p-8 text-primary-content">
          <h3 class="text-2xl font-bold mb-4">Ready to get started?</h3>
          <p class="mb-6 opacity-90">
            Join our growing community of developers who trust EmailConnect for their applications
          </p>
          <div class="flex flex-col sm:flex-row gap-4 justify-center">
            <router-link to="/register" class="btn btn-accent btn-lg">
              Get started today
            </router-link>
            <a href="/docs" class="btn btn-outline btn-lg text-primary-content border-primary-content hover:bg-primary-content hover:text-primary">
              View documentation
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// Pricing section with plans and FAQ
</script>
