<script setup lang="ts">
import { ref, onMounted, computed, watch } from 'vue'
import DataTable from '../ui/DataTable.vue'
import type { TableColumn } from '../ui/DataTable.vue'
import type { Domain, Alias } from '../../types'

// State
const domains = ref<Domain[]>([])
const aliases = ref<Alias[]>([])
const logs = ref<any[]>([])
const selectedDomain = ref('')
const selectedAlias = ref('')
const isLoading = ref(true)
const isLoadingLogs = ref(false)

// Computed
const filteredAliases = computed(() => {
  if (!selectedDomain.value) return []
  return aliases.value.filter(alias => alias.domainId === selectedDomain.value)
})

// Load initial data
const loadDomains = async () => {
  try {
    const response = await fetch('/api/domains')
    const data = await response.json()
    domains.value = data.domains || []
  } catch (error) {
    console.error('Failed to load domains:', error)
  }
}

const loadAliases = async () => {
  try {
    const response = await fetch('/api/aliases')
    const data = await response.json()
    aliases.value = data.aliases || []
  } catch (error) {
    console.error('Failed to load aliases:', error)
  }
}

const loadLogs = async () => {
  if (!selectedDomain.value) {
    logs.value = []
    return
  }

  try {
    isLoadingLogs.value = true
    const params = new URLSearchParams({
      domainId: selectedDomain.value
    })
    
    if (selectedAlias.value) {
      params.append('aliasId', selectedAlias.value)
    }

    const response = await fetch(`/api/logs?${params}`)
    const data = await response.json()
    logs.value = data.logs || []
  } catch (error) {
    console.error('Failed to load logs:', error)
    logs.value = []
  } finally {
    isLoadingLogs.value = false
  }
}

// Watch for changes in selections
watch(selectedDomain, () => {
  selectedAlias.value = '' // Reset alias when domain changes
  loadLogs()
})

watch(selectedAlias, () => {
  loadLogs()
})

onMounted(async () => {
  isLoading.value = true
  await Promise.all([loadDomains(), loadAliases()])

  // Check for pre-selected domain/alias from navigation
  const preSelectedDomain = sessionStorage.getItem('logsView_selectedDomain')
  const preSelectedAlias = sessionStorage.getItem('logsView_selectedAlias')

  if (preSelectedDomain) {
    selectedDomain.value = preSelectedDomain
    sessionStorage.removeItem('logsView_selectedDomain')

    if (preSelectedAlias) {
      selectedAlias.value = preSelectedAlias
      sessionStorage.removeItem('logsView_selectedAlias')
    }

    // Load logs for the pre-selected domain/alias
    await loadLogs()
  }

  isLoading.value = false
})

// Quick access method for external calls
const viewLogsFor = (domainId: string, aliasId?: string) => {
  selectedDomain.value = domainId
  if (aliasId) {
    selectedAlias.value = aliasId
  }
}

// Get alias display name for a log entry
const getAliasDisplayName = (log: any) => {
  if (!log.toAddresses || log.toAddresses.length === 0) return 'Unknown'

  const toAddress = log.toAddresses[0] // Use first address
  const selectedDomainData = domains.value.find(d => d.id === selectedDomain.value)

  if (!selectedDomainData) return toAddress

  // Extract the local part (before @)
  const localPart = toAddress.split('@')[0]

  // Check if it's a catch-all (wildcard)
  if (localPart === '*' || toAddress.startsWith('*@')) {
    return 'catch-all'
  }

  return localPart
}

// Table columns configuration
const columns: TableColumn<any>[] = [
  {
    key: 'createdAt',
    label: 'Timestamp',
    sortable: true,
    render: (value: any) => {
      return new Date(value).toLocaleString()
    }
  },
  {
    key: 'alias',
    label: 'Alias',
    sortable: true,
    render: (_value: any, row: any) => {
      if (row.isTestWebhook) {
        return `<span class="badge badge-primary badge-sm">Test Webhook</span>`
      }
      return getAliasDisplayName(row)
    }
  },
  {
    key: 'fromAddress',
    label: 'From',
    sortable: true,
    render: (value: any) => {
      return `<div class="truncate max-w-[200px]" title="${value}">${value}</div>`
    }
  },
  {
    key: 'subject',
    label: 'Subject',
    sortable: true,
    render: (value: any) => {
      const displaySubject = value || '(no subject)'
      return `<div class="truncate max-w-[300px]" title="${displaySubject}">${displaySubject}</div>`
    }
  },
  {
    key: 'deliveryStatus',
    label: 'Status',
    sortable: true,
    render: (value: any, row: any) => {
      const badgeClass = {
        'DELIVERED': 'badge-success',
        'FAILED': 'badge-error',
        'PENDING': 'badge-warning',
        'RETRYING': 'badge-warning',
        'EXPIRED': 'badge-neutral'
      }[value] || 'badge-neutral'

      const statusText = row.isTestWebhook && value === 'DELIVERED' ? 'PROCESSED' : value
      return `<div class="badge ${badgeClass}">${statusText}</div>`
    }
  },
  {
    key: 'actions',
    label: '',
    width: '100px',
    render: (_value: any, row: any) => {
      if (row.isTestWebhook && row.webhookPayload) {
        return `
          <button type="button"
                  class="btn btn-outline btn-primary btn-xs"
                  data-action="viewPayload"
                  data-log-id="${row.id}">
            View Payload
          </button>
        `
      }
      return ''
    }
  }
]

const selectedPayload = ref<any>(null)
const showPayloadModal = ref(false)

const handleRowClick = (log: any, _index: number) => {
  // Optional: Handle row click for future log details modal
}

const handleActionClick = (event: Event) => {
  const target = event.target as HTMLElement
  const action = target.getAttribute('data-action')

  if (action === 'viewPayload') {
    const logId = target.getAttribute('data-log-id')
    const log = logs.value.find(l => l.id === logId)

    if (log && log.webhookPayload) {
      selectedPayload.value = log.webhookPayload
      showPayloadModal.value = true
    }
  }
}

const closePayloadModal = () => {
  showPayloadModal.value = false
  selectedPayload.value = null
}

const copyPayloadToClipboard = async () => {
  try {
    await window.navigator.clipboard.writeText(JSON.stringify(selectedPayload.value, null, 2))
    // Could add a toast notification here if needed
  } catch (error) {
    console.error('Failed to copy to clipboard:', error)
  }
}

// Expose methods for parent components
defineExpose({
  viewLogsFor,
  refresh: () => Promise.all([loadDomains(), loadAliases(), loadLogs()])
})
</script>

<template>
  <div class="space-y-6">
    <!-- Loading state -->
    <div v-if="isLoading" class="flex justify-center py-8">
      <span class="loading loading-spinner loading-lg"></span>
    </div>

    <div v-else class="space-y-6">
      <!-- Selectors -->
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <!-- Domain selector -->
        <div class="form-control w-full">
          <label class="label">
            <span class="label-text font-semibold text-base-content">Select domain</span>
          </label>
          <select v-model="selectedDomain" class="select select-bordered w-full focus:border-blue-500 focus:ring-2 focus:ring-blue-200">
            <option value="">Choose a domain...</option>
            <option v-for="domain in domains" :key="domain.id" :value="domain.id">
              {{ domain.domainName || domain.domain }}
            </option>
          </select>
        </div>

        <!-- Alias selector -->
        <div v-if="false" class="form-control w-full">
          <label class="label">
            <span class="label-text font-semibold text-base-content">Select alias</span>
          </label>
          <select
            v-model="selectedAlias"
            class="select select-bordered w-full focus:border-blue-500 focus:ring-2 focus:ring-blue-200"
            :disabled="!selectedDomain || filteredAliases.length === 0"
            :class="{ 'select-disabled': !selectedDomain || filteredAliases.length === 0 }"
          >
            <option value="">Show all</option>
            <option v-for="alias in filteredAliases" :key="alias.id" :value="alias.id">
              {{ alias.email.includes('*@') ? 'catch-all' : alias.email.split('@')[0] }}
            </option>
          </select>
        </div>
      </div>

      <!-- Logs viewer -->
      <div class="card bg-base-100 border border-base-300 rounded-lg">
        <div class="card-header px-6 py-4 border-b border-base-300">
          <h3 class="text-lg font-medium">Email logs</h3>
        </div>
        <div class="card-body p-0">
          <!-- Loading logs -->
          <div v-if="isLoadingLogs" class="flex justify-center py-8">
            <span class="loading loading-spinner loading-md"></span>
          </div>

          <!-- No domain selected -->
          <div v-else-if="!selectedDomain" class="p-8 text-center text-base-content/60">
            <svg class="w-12 h-12 mx-auto mb-4 text-base-content/40" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            <p>Select a domain to view email logs</p>
          </div>

          <!-- No logs found -->
          <div v-else-if="logs.length === 0" class="p-8 text-center text-base-content/60">
            <svg class="w-12 h-12 mx-auto mb-4 text-base-content/40" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2 2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-4.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4" />
            </svg>
            <p>No email logs found for the selected criteria</p>
          </div>

          <!-- Logs table -->
          <div v-else class="bg-base-100" @click="handleActionClick">
            <DataTable
              :columns="columns"
              :data="logs"
              :loading="isLoadingLogs"
              empty-message="No logs found for the selected criteria."
              default-sort-column="createdAt"
              default-sort-direction="desc"
              @row-click="handleRowClick"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- Webhook Payload Modal -->
    <div v-if="showPayloadModal" class="modal modal-open">
      <div class="modal-box max-w-4xl">
        <h3 class="font-bold text-lg mb-4">Test Webhook Payload</h3>

        <div class="bg-base-200 rounded-lg p-4 max-h-96 overflow-auto">
          <pre class="text-sm"><code>{{ JSON.stringify(selectedPayload, null, 2) }}</code></pre>
        </div>

        <div class="modal-action">
          <button @click="closePayloadModal" class="btn">Close</button>
          <button
            @click="copyPayloadToClipboard"
            class="btn btn-primary"
          >
            Copy JSON
          </button>
        </div>
      </div>
      <div class="modal-backdrop" @click="closePayloadModal"></div>
    </div>
  </div>
</template>
