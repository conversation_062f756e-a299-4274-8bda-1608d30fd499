<script setup lang="ts" generic="T extends Record<string, any>">
import { computed } from 'vue'
import { useTableSort } from '@composables/useTableSort'

export interface TableColumn<T = any> {
  key: string
  label: string
  sortable?: boolean
  width?: string
  align?: 'left' | 'center' | 'right'
  render?: (value: any, row: T, index: number) => string
}

interface Props {
  columns: TableColumn<T>[]
  data: T[]
  loading?: boolean
  emptyMessage?: string
  striped?: boolean
  hover?: boolean
  compact?: boolean
  defaultSortColumn?: string
  defaultSortDirection?: 'asc' | 'desc'
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  emptyMessage: 'No data available',
  striped: true,
  hover: true,
  compact: false,
  defaultSortColumn: '',
  defaultSortDirection: 'asc'
})

const emit = defineEmits<{
  rowClick: [row: T, index: number]
}>()

const { sortedData, sortColumn, sortDirection, handleSort } = useTableSort(
  () => props.data,
  props.defaultSortColumn || props.columns[0]?.key || '',
  props.defaultSortDirection
)

const tableClasses = computed(() => {
  const baseClasses = 'table w-full'
  const modifiers: string[] = []

  if (props.striped) modifiers.push('table-zebra')
  if (props.hover) modifiers.push('table-hover')
  if (props.compact) modifiers.push('table-compact')

  return [baseClasses, ...modifiers].join(' ')
})

const getSortIcon = (columnKey: string) => {
  if (sortColumn.value !== columnKey) {
    return 'M7 10l5 5 5-5H7z' // Default sort icon
  }
  
  return sortDirection.value === 'asc'
    ? 'M7 14l5-5 5 5H7z' // Up arrow
    : 'M7 10l5 5 5-5H7z'  // Down arrow
}

const getCellValue = (row: T, column: TableColumn<T>, index: number) => {
  const value = row[column.key]
  
  if (column.render) {
    return column.render(value, row, index)
  }
  
  return value
}

const getAlignmentClass = (align?: string) => {
  switch (align) {
    case 'center':
      return 'text-center'
    case 'right':
      return 'text-right'
    default:
      return 'text-left'
  }
}
</script>

<template>
  <div class="overflow-x-auto">
    <table :class="tableClasses">
      <!-- Table Header -->
      <thead>
        <tr>
          <th
            v-for="column in columns"
            :key="column.key"
            :class="[
              getAlignmentClass(column.align),
              column.sortable ? 'cursor-pointer select-none hover:bg-base-200' : ''
            ]"
            :style="column.width ? { width: column.width } : undefined"
            @click="column.sortable ? handleSort(column.key) : undefined"
          >
            <div class="flex items-center gap-2">
              <span>{{ column.label }}</span>
              <svg
                v-if="column.sortable"
                class="w-4 h-4 transition-transform"
                :class="{
                  'text-primary': sortColumn === column.key,
                  'text-base-content/50': sortColumn !== column.key,
                  'rotate-180': sortColumn === column.key && sortDirection === 'desc'
                }"
                fill="currentColor"
                viewBox="0 0 24 24"
              >
                <path :d="getSortIcon(column.key)" />
              </svg>
            </div>
          </th>
        </tr>
      </thead>
      
      <!-- Table Body -->
      <tbody>
        <!-- Loading State -->
        <tr v-if="loading">
          <td :colspan="columns.length" class="text-center py-8">
            <div class="flex items-center justify-center gap-2">
              <span class="loading loading-spinner loading-sm"></span>
              <span>Loading...</span>
            </div>
          </td>
        </tr>
        
        <!-- Empty State -->
        <tr v-else-if="sortedData.length === 0">
          <td :colspan="columns.length" class="text-center py-8 text-base-content/60">
            {{ emptyMessage }}
          </td>
        </tr>
        
        <!-- Data Rows -->
        <tr
          v-else
          v-for="(row, index) in sortedData"
          :key="index"
          class="cursor-pointer"
          @click="emit('rowClick', row, index)"
        >
          <td
            v-for="column in columns"
            :key="column.key"
            :class="getAlignmentClass(column.align)"
          >
            <div v-html="getCellValue(row, column, index)" />
          </td>
        </tr>
      </tbody>
    </table>
  </div>
</template>
