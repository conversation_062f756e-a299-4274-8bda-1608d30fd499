<template>
  <div class="space-y-4">
    <!-- Status Card -->
    <div class="card bg-base-100 border border-base-300">
      <div class="card-body p-4">
        <div class="flex items-center space-x-3">
          <div class="loading loading-spinner loading-sm text-primary" v-if="isWaiting"></div>
          <svg v-else class="w-5 h-5 text-warning" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                  d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <div>
            <h4 class="font-medium text-base-content">
              {{ isWaiting ? 'Waiting for your email...' : 'Send an email to your test address' }}
            </h4>
            <p class="text-sm text-base-content/70">
              {{ isWaiting ? 'We\'ll detect it automatically when it arrives' : 'Use any email client to send a test message' }}
            </p>
            <p v-if="!isWaiting" class="text-xs text-info mt-2">
          💡 <strong>Tip:</strong> Click "Watch for my email" and we'll automatically detect when it arrives!
            </p>
          </div>
        </div>
      </div>
    </div>

    <!-- Manual Actions -->
    <div class="flex flex-wrap gap-2 justify-center">
      <!-- Primary action: Start monitoring OR mark as done -->
      <button
        v-if="!isWaiting"
        @click="startWaiting"
        class="btn btn-primary btn-sm"
      >
        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
        </svg>
        Watch for my email
      </button>

      <!-- While waiting: Manual completion option -->
      <button
        v-if="isWaiting"
        @click="markAsCompleted"
        class="btn btn-primary btn-sm"
      >
        <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
        </svg>
        Done - I sent it
      </button>

      <!-- Secondary actions -->
      <button
        v-if="isWaiting"
        @click="checkForEmails"
        class="btn btn-outline btn-sm"
        :disabled="isChecking"
      >
        <div v-if="isChecking" class="loading loading-spinner loading-xs mr-1"></div>
        <svg v-else class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
        </svg>
        Check now
      </button>

      <button
        v-if="isWaiting"
        @click="stopWaiting"
        class="btn btn-ghost btn-sm"
      >
        Stop watching
      </button>

      <!-- Skip option for non-waiting state -->
      <button
        v-if="!isWaiting"
        @click="markAsCompleted"
        class="btn btn-ghost btn-sm"
      >
        Skip this step
      </button>
    </div>

    <!-- Help Text -->
    <div class="text-center">
      <p class="text-xs text-base-content/60">
        {{ isWaiting
          ? '💡 We check for new emails every 10 seconds. Usually takes 30-60 seconds to process.'
          : '💡 Choose "Watch for my email" for automatic detection, or "Done" to proceed manually.'
        }}
      </p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { useToast } from '@composables/useToast'

interface Props {
  step?: any
}

const props = defineProps<Props>()

const emit = defineEmits<{
  complete: []
}>()

// State
const isWaiting = ref(false)
const isChecking = ref(false)
const checkInterval = ref<number | null>(null)
const startTime = ref<Date | null>(null)
const initialEmailCount = ref(0)
const { success, error } = useToast()

// Methods
const startWaiting = async () => {
  // Record the current state before we start waiting
  startTime.value = new Date()

  // Get initial email count to detect NEW emails
  try {
    const response = await fetch('/api/logs?limit=1')
    const data = await response.json()
    initialEmailCount.value = data.logs?.length || 0
  } catch (error) {
    console.warn('Failed to get initial email count:', error)
    initialEmailCount.value = 0
  }

  isWaiting.value = true
  startPolling()
}

const startPolling = () => {
  // Check immediately
  checkForEmails()
  
  // Then check every 10 seconds
  checkInterval.value = window.setInterval(() => {
    checkForEmails()
  }, 10000)

  // Stop polling after 5 minutes
  setTimeout(() => {
    stopPolling()
  }, 300000)
}

const stopPolling = () => {
  if (checkInterval.value) {
    clearInterval(checkInterval.value)
    checkInterval.value = null
  }
  isWaiting.value = false
}

const stopWaiting = () => {
  stopPolling()
}

const checkForEmails = async () => {
  if (isChecking.value) return

  isChecking.value = true

  try {
    // Check logs for recent emails since we started waiting
    const response = await fetch('/api/logs?limit=10')
    const data = await response.json()

    if (data.logs && data.logs.length > 0) {
      // Check if we have NEW emails since we started waiting
      const newEmails = data.logs.filter((log: any) => {
        const logTime = new Date(log.timestamp || log.createdAt)
        return startTime.value && logTime > startTime.value
      })

      if (newEmails.length > 0) {
        // Found new emails! Complete the step
        success(`🎉 Email received! Let's see your webhook in action...`)
        stopPolling()
        emit('complete')
      }
    }
  } catch (err) {
    console.error('Failed to check for emails:', err)
  } finally {
    isChecking.value = false
  }
}

const markAsCompleted = () => {
  stopPolling()
  emit('complete')
}

// Cleanup
onUnmounted(() => {
  stopPolling()
})

// Auto-start if this is the active step
onMounted(() => {
  // Could auto-start waiting, but let user initiate
})
</script>

<style scoped>
.loading-dots {
  --loading-size: 1rem;
}
</style>
