<template>
  <div class="card bg-base-100">
    <div class="card-body">
      <h2 class="card-title mb-6">Storage configuration</h2>
      
      <!-- Free Plan Limitations - Moved to top -->
      <div class="bg-info/10 border border-info/20 rounded-lg p-4 mb-6">
        <h4 class="text-sm font-semibold text-info mb-2">Free plan limitations</h4>
        <div class="space-y-2 text-sm text-base-content/70">
          <div class="flex items-center space-x-2">
            <svg class="w-4 h-4 text-info" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <span>Maximum attachment size: <strong>128KB</strong></span>
          </div>
          <div class="flex items-center space-x-2">
            <svg class="w-4 h-4 text-info" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <span>Text-based files only: <strong>PDF, TXT, CSV, MD, ICS, JSON, XML</strong></span>
          </div>
          <div class="flex items-center space-x-2">
            <svg class="w-4 h-4 text-info" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <span>Attachments included as <strong>base64</strong> in webhook payload</span>
          </div>
        </div>
      </div>

      <!-- Attachment Processing Configuration (Pro Plan Preview) -->
      <div class="bg-base-200/40 rounded-lg p-6">
        <h3 class="text-lg font-semibold mb-4">
          <span class="badge badge-success bg-primary/10 text-base-content badge-sm badge-outline mr-1">Pro</span>
          Attachment processing 
        </h3>
        <p class="text-sm text-base-content/70 mb-6">Upcoming Pro plan features for attachment handling</p>

        <div class="space-y-6 opacity-60">
          <!-- Max File Size Slider (Disabled) -->
          <div>
            <label class="label">
              <span class="label-text">Maximum attachment size</span>
            </label>
            <div class="flex items-center space-x-4">
              <input
                type="range"
                min="0.1"
                max="10"
                step="0.1"
                value="2"
                disabled
                class="range range-primary flex-1"
              >
              <span class="text-sm font-medium min-w-[4rem]">10 MB</span>
            </div>
          </div>

          <!-- Allow Media Files Toggle (Disabled) -->
          <div>
            <label class="label cursor-pointer justify-start">
              <input
                type="checkbox"
                disabled
                class="checkbox checkbox-primary mr-3"
              />
              <div class="flex flex-col">
                <span class="label-text">Allow media files</span>
                <span class="label-text-alt text-xs">Process images, videos, and other media attachments</span>
              </div>
            </label>
          </div>

          <!-- Storage Provider (Disabled for Free Users) -->
          <div class="opacity-60">
            <label class="label">
              <span class="label-text">Storage provider</span>
            </label>
            <select disabled class="select select-bordered w-full">
              <option>No external storage (Free plan)</option>
            </select>
            <label class="label">
              <span class="label-text-alt">
                Configure any S3-compatible storage for large attachments
              </span>
            </label>
          </div>

          <!-- Retention Policy (Disabled for Free Users) -->
          <div class="opacity-60">
            <label class="label">
              <span class="label-text">Attachment retention</span>
            </label>
            <select disabled class="select select-bordered w-full">
              <option>No retention options (Free plan)</option>
            </select>
          </div>

          <!-- Save Button [todo] :disabled="saving"-->
          <div class="pt-4">
            <button
              type="button"
              @click="saveSettings"
              disabled
              class="btn btn-primary"
            >
              {{ saving ? 'Saving...' : 'Save storage settings' }}
            </button>
          </div>
        </div>
      </div>

      <!-- Future Features Preview -->
      <div class="bg-base-200/40 rounded-lg p-6 mt-6">
        <h3 class="text-lg font-semibold mb-4">Coming soon</h3>
        <div class="space-y-3">
          <div class="flex items-center space-x-3 opacity-60">
            <input type="checkbox" disabled class="checkbox checkbox-primary">
            <span>Custom S3 bucket configuration</span>
          </div>
          <div class="flex items-center space-x-3 opacity-60">
            <input type="checkbox" disabled class="checkbox checkbox-primary">
            <span>Domain-specific storage settings</span>
          </div>
          <div class="flex items-center space-x-3 opacity-60">
            <input type="checkbox" disabled class="checkbox checkbox-primary">
            <span>Alias-level attachment policies</span>
          </div>
          <div class="flex items-center space-x-3 opacity-60">
            <input type="checkbox" disabled class="checkbox checkbox-primary">
            <span>Advanced retention policies</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'

// State
const saving = ref(false)
const settings = ref({
  maxInlineSize: 1.0,
  storageProvider: 'default',
  retentionHours: 1
})

// Methods

const saveSettings = async () => {
  saving.value = true
  try {
    // TODO: Implement actual save functionality
    console.log('Saving storage settings:', settings.value)

    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000))

    // Show success message
    alert('Storage settings saved successfully!')
  } catch (error) {
    console.error('Failed to save storage settings:', error)
    alert('Failed to save settings. Please try again.')
  } finally {
    saving.value = false
  }
}

onMounted(() => {
  // Component initialization
})
</script>

<style scoped>
/* Storage section specific styles */
.stat {
  padding: 1rem;
}
</style>
