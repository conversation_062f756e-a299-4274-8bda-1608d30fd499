<script setup lang="ts">
import { ref } from 'vue'
import { useForm } from '@composables/useForm'
import { useWebhookApi } from '@composables/useApi'
import { useDataRefresh } from '@composables/useDataRefresh'
import { useMetrics } from '@composables/useMetrics'
import type { CreateWebhookRequest } from '@types'

interface Props {
  initialData?: Partial<CreateWebhookRequest & {
    context?: string
    domainForm?: any
    id?: string
  }>
  isEditMode?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  initialData: () => ({}),
  isEditMode: false
})

const emit = defineEmits<{
  success: [webhook: any]
  cancel: []
}>()

// Form setup
const { values, errors, isSubmitting, isValid, setFieldValue, handleSubmit } = useForm<CreateWebhookRequest>({
  name: props.initialData.name || '',
  url: props.initialData.url || '',
  description: props.initialData.description || ''
}, [
  { name: 'name', label: 'Webhook name', type: 'text', required: true, validation: { minLength: 2, maxLength: 100 } },
  { name: 'url', label: 'Webhook URL', type: 'url', required: true },
  { name: 'description', label: 'Description', type: 'text', validation: { maxLength: 500 } }
])

// API setup
const { createWebhook, updateWebhook } = useWebhookApi()
const { refreshWithSuccess } = useDataRefresh()
const { refreshMetrics } = useMetrics()

// Methods
const onSubmit = async () => {
  await handleSubmit(async (formData) => {
    let result: any
    let webhook: any

    if (props.isEditMode && props.initialData.id) {
      // Edit mode - update existing webhook
      result = await updateWebhook(props.initialData.id, formData)
      webhook = result.webhook
      emit('success', webhook)
      refreshWithSuccess('Webhook updated successfully!', 'webhooks')

      // If URL changed, webhook needs re-verification
      if (formData.url !== props.initialData.url) {
        setTimeout(() => {
          window.openModal('webhook-verification', {
            webhookId: webhook.id,
            webhookUrl: webhook.url,
            webhookName: webhook.name
          });
        }, 700)
      }
    } else {
      // Create mode - create new webhook
      result = await createWebhook(formData) as any
      webhook = result.webhook
      emit('success', webhook)
      refreshWithSuccess('Webhook created successfully!', 'webhooks')

      // Auto-open webhook verification modal for new unverified webhooks
      setTimeout(() => {
        window.openModal('webhook-verification', {
          webhookId: webhook.id,
          webhookUrl: webhook.url,
          webhookName: webhook.name
        });
      }, 700)
    }

    // Force refresh metrics to update tab counts immediately
    setTimeout(async () => {
      try {
        await refreshMetrics()
      } catch (error) {
        console.error('Failed to refresh metrics after webhook operation:', error)
      }
    }, 500)
  })
}
</script>

<template>
  <form @submit.prevent="onSubmit" class="space-y-6">
    <!-- Webhook Name -->
    <div class="form-control">
      <label class="label">
        <span class="label-text">Webhook name</span>
      </label>
      <input
        :value="values.name"
        @input="setFieldValue('name', ($event.target as HTMLInputElement).value)"
        type="text"
        placeholder="My webhook"
        class="w-full input input-bordered"
        :class="{ 'input-error': errors.name }"
        required
      />
      <div v-if="errors.name" class="label">
        <span class="label-text-alt text-error">{{ errors.name }}</span>
      </div>
    </div>

    <!-- Webhook URL -->
    <div class="form-control">
      <label class="label">
        <span class="label-text">Webhook URL</span>
      </label>
      <input
        :value="values.url"
        @input="setFieldValue('url', ($event.target as HTMLInputElement).value)"
        type="url"
        placeholder="https://your-app.com/webhook"
        class="w-full input input-bordered"
        :class="{ 'input-error': errors.url }"
        required
      />
      <div v-if="errors.url" class="label">
        <span class="label-text-alt text-error">{{ errors.url }}</span>
      </div>
      <div class="label">
        <span class="label-text-alt text-xs">The endpoint where emails will be sent</span>
      </div>
    </div>

    <!-- Description -->
    <div class="form-control">
      <label class="label">
        <span class="label-text">Description (optional)</span>
      </label>
      <textarea
        :value="values.description"
        @input="setFieldValue('description', ($event.target as HTMLTextAreaElement).value)"
        placeholder="Brief description of this webhook..."
        rows="3"
        class="w-full textarea textarea-bordered"
        :class="{ 'textarea-error': errors.description }"
      />
      <div v-if="errors.description" class="label">
        <span class="label-text-alt text-error">{{ errors.description }}</span>
      </div>
    </div>

    <!-- Form Actions -->
    <div class="modal-action">
      <button
        type="button"
        @click="emit('cancel')"
        class="btn btn-ghost"
      >
        Cancel
      </button>
      <button
        type="submit"
        class="btn btn-primary"
        :disabled="!isValid || isSubmitting"
        :class="{ loading: isSubmitting }"
      >
        {{ isSubmitting
          ? (isEditMode ? 'Updating...' : 'Creating...')
          : (isEditMode ? 'Update webhook' : 'Create webhook')
        }}
      </button>
    </div>
  </form>
</template>