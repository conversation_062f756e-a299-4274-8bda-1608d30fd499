<script setup lang="ts">
import { ref, computed } from 'vue'
import { useDomain<PERSON>pi } from '@composables/useApi'
import { useDataRefresh } from '@composables/useDataRefresh'
import { useToast } from '@composables/useToast'
import WebhookVerificationSteps from '@/components/shared/WebhookVerificationSteps.vue'
import type { DomainVerificationData } from '@types'

interface Props {
  domainData: DomainVerificationData
}

const props = defineProps<Props>()

const emit = defineEmits<{
  close: []
}>()

// State
const currentStep = ref(1)
const showDnsDetails = ref(false)
const webhookVerified = ref(false)

// Computed
const maxStep = computed(() => props.domainData.webhookNeedsVerification ? 3 : 2)
const txtRecord = computed(() => `verify-ec=${props.domainData.domain}`)
const mxRecord = computed(() => '10 emailconnect.eu')

// API
const { verifyDomain } = useDomainApi()

// Methods
const copyToClipboard = async (text: string) => {
  try {
    await navigator.clipboard.writeText(text)
    // Could add toast notification here
  } catch (error) {
    console.error('Failed to copy to clipboard:', error)
  }
}

const nextStep = () => {
  if (currentStep.value < maxStep.value) {
    currentStep.value++
  }
}

const prevStep = () => {
  if (currentStep.value > 1) {
    currentStep.value--
  }
}

const handleWebhookVerified = () => {
  webhookVerified.value = true

  // Update webhook status and trigger refresh
  const { updateItem, triggerRefresh } = useDataRefresh()
  updateItem('webhooks', props.domainData.webhookId, { verified: true })
  triggerRefresh('webhooks')

  // Move to next step after webhook verification
  if (currentStep.value === 2) {
    currentStep.value++
  }
}

const handleWebhookError = (message: string) => {
  console.error('Webhook verification error:', message)
}

const completeVerification = async () => {
  try {
    await verifyDomain(props.domainData.domainId)
    emit('close')

    // Update the specific domain item to verified status
    const { updateItem } = useDataRefresh()
    updateItem('domains', props.domainData.domainId, {
      verified: true,
      verificationStatus: 'VERIFIED'
    })

    // Show success message
    const { success } = useToast()
    success('Domain configuration completed!')
  } catch (error) {
    console.error('Domain verification failed:', error)
  }
}
</script>

<template>
  <div class="space-y-6">
    <!-- Progress indicator -->
    <div class="flex items-center justify-between mb-6">
      <div class="flex items-center space-x-2">
        <div
          v-for="step in maxStep"
          :key="step"
          class="flex items-center"
        >
          <div
            class="flex items-center justify-center w-8 h-8 text-sm font-medium rounded-full"
            :class="step <= currentStep ? 'bg-primary text-black' : 'bg-base-300 text-base-content/50'"
          >
            {{ step }}
          </div>
          <div
            v-if="step < maxStep"
            class="w-8 h-0.5 mx-2"
            :class="step < currentStep ? 'bg-primary' : 'bg-base-300'"
          />
        </div>
      </div>
      <div class="text-sm text-base-content/70">
        Step {{ currentStep }} of {{ maxStep }}
      </div>
    </div>

    <!-- Step 1: DNS Configuration -->
    <div v-show="currentStep === 1" class="space-y-4">
      <h3 class="text-lg font-medium text-base-content">Configure DNS records</h3>
      <p class="text-sm text-base-content/70">
        Add these DNS records to verify domain ownership and enable email routing.
      </p>

      <!-- Quick summary -->
      <div class="p-4 border border-base-300 rounded-lg bg-base-200/40">
        <h4 class="mb-2 text-sm font-medium text-base-content">Required DNS records</h4>
        <div class="space-y-2 text-sm">
          <div class="flex items-center justify-between">
            <span>TXT Record:</span>
            <div class="flex items-center space-x-2">
              <code class="px-2 py-1 text-xs text-base-content bg-base-300 rounded">{{ txtRecord }}</code>
              <button @click="copyToClipboard(txtRecord)" class="btn btn-xs btn-primary">Copy</button>
            </div>
          </div>
          <div class="flex items-center justify-between">
            <span>MX Record:</span>
            <div class="flex items-center space-x-2">
              <code class="px-2 py-1 text-xs text-base-content bg-base-300 rounded">{{ mxRecord }}</code>
              <button @click="copyToClipboard(mxRecord)" class="btn btn-xs btn-success">Copy</button>
            </div>
          </div>
        </div>
      </div>

      <!-- Collapsible detailed instructions -->
      <div class="collapse collapse-arrow border border-base-300 rounded-lg bg-base-200/40">
        <input type="checkbox" v-model="showDnsDetails" />
        <div class="text-sm font-medium collapse-title">
          Detailed DNS configuration instructions
        </div>
        <div class="space-y-4 collapse-content">
          <!-- TXT Record -->
          <div class="p-4 border border-base-300 rounded-lg bg-base-200/40">
            <h4 class="mb-2 text-sm font-medium text-base-content">1. TXT record (domain verification)</h4>
            <div class="grid grid-cols-3 gap-4 mb-2 text-sm">
              <div>
                <span class="font-medium text-base-content">Type:</span>
                <div class="px-2 py-1 font-mono bg-base-300 border rounded">TXT</div>
              </div>
              <div>
                <span class="font-medium text-base-content">Name:</span>
                <div class="px-2 py-1 font-mono bg-base-300 border rounded">@</div>
              </div>
              <div>
                <span class="font-medium text-base-content">TTL:</span>
                <div class="px-2 py-1 font-mono bg-base-300 border rounded">300</div>
              </div>
            </div>
            <div>
              <span class="font-medium text-base-content">Value:</span>
              <div class="flex items-center mt-1 space-x-2">
                <div class="flex-1 px-2 py-1 font-mono text-xs break-all bg-base-300 border rounded">
                  {{ txtRecord }}
                </div>
                <button @click="copyToClipboard(txtRecord)" class="btn btn-xs btn-primary">Copy</button>
              </div>
            </div>
          </div>

          <!-- MX Record -->
          <div class="p-4 border border-base-300 rounded-lg bg-base-200/40">
            <h4 class="mb-2 text-sm font-medium text-base-content">2. MX record (email routing)</h4>
            <div class="grid grid-cols-3 gap-4 mb-2 text-sm">
              <div>
                <span class="font-medium text-base-content">Type:</span>
                <div class="px-2 py-1 font-mono bg-base-300 border rounded">MX</div>
              </div>
              <div>
                <span class="font-medium text-base-content">Name:</span>
                <div class="px-2 py-1 font-mono bg-base-300 border rounded">@</div>
              </div>
              <div>
                <span class="font-medium text-base-content">TTL:</span>
                <div class="px-2 py-1 font-mono bg-base-300 border rounded">300</div>
              </div>
            </div>
            <div>
              <span class="font-medium text-base-content">Value:</span>
              <div class="flex items-center mt-1 space-x-2">
                <div class="flex-1 px-2 py-1 font-mono text-xs bg-base-300 border rounded">
                  {{ mxRecord }}
                </div>
                <button @click="copyToClipboard(mxRecord)" class="btn btn-xs btn-success">Copy</button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Step 2: Webhook Verification (if needed) -->
    <div v-if="domainData.webhookNeedsVerification" v-show="currentStep === 2" class="space-y-4">
      <h3 class="text-lg font-medium text-base-content">Verify webhook endpoint</h3>
      <p class="text-sm text-base-content/70">We need to test your webhook endpoint to ensure it can receive emails.</p>

      <!-- Use shared webhook verification component -->
      <WebhookVerificationSteps
        :webhook-id="domainData.webhookId"
        :webhook-url="domainData.webhookUrl"
        :webhook-name="domainData.webhookName"
        @verified="handleWebhookVerified"
        @error="handleWebhookError"
      />
    </div>

    <!-- Step 3: Final Verification (or Step 2 if no webhook) -->
    <div v-show="currentStep === maxStep" class="space-y-4">
      <h3 class="text-lg font-medium text-base-content">Ready for verification</h3>
      <p class="text-sm text-base-content/70">
        We'll check your DNS records{{ domainData.webhookNeedsVerification ? ' and webhook endpoint' : '' }} to complete the setup automatically.
      </p>

      <div class="p-4 border border-base-300 rounded-lg bg-base-200/40">
        <div class="flex">
          <svg class="w-5 h-5 mr-2 text-green-400" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
          </svg>
          <div>
            <h4 class="text-sm font-medium text-base-content">Ready for verification</h4>
            <p class="text-sm text-base-content/70">
              We'll check your DNS records{{ domainData.webhookNeedsVerification ? ' and webhook endpoint' : '' }} to complete the setup.
            </p>
          </div>
        </div>
      </div>
    </div>

    <!-- Navigation buttons -->
    <div class="flex items-center justify-between pt-4 border-t">
      <button
      v-if="currentStep > 1"
      @click="prevStep"
      class="btn btn-ghost"
      >
      Previous
      </button>

      <div
      class="flex flex-1 items-center"
      :class="{
        'justify-end': (currentStep < maxStep && !(currentStep === 2 && domainData.webhookNeedsVerification)) || currentStep === maxStep,
        'justify-between': currentStep > 1 && ((currentStep < maxStep && !(currentStep === 2 && domainData.webhookNeedsVerification)) || currentStep === maxStep)
      }"
      >
      <button
        v-if="currentStep < maxStep && !(currentStep === 2 && domainData.webhookNeedsVerification)"
        @click="nextStep"
        class="btn btn-primary"
      >
        Next
      </button>
      <button
        v-if="currentStep === maxStep"
        @click="completeVerification"
        class="btn btn-success"
      >
        Complete verification
      </button>
      </div>
    </div>
  </div>
</template>