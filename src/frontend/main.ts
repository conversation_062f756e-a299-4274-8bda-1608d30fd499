import { createApp } from 'vue'
import { createPinia } from 'pinia'
import App from './App.vue'
import router from './router'
import './style.css'
import './assets-manifest' // Import assets to ensure they're processed by Vite

// Create and mount the consolidated dashboard app
const app = createApp(App)

// Add Vue Router and Pinia
const pinia = createPinia()
app.use(pinia)
app.use(router)

// Mount the single Vue app
app.mount('#app')
