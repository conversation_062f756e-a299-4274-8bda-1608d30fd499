export const domainSchemas = {
  // Domain response schemas
  DomainResponse: {
    type: 'object',
    properties: {
      id: { type: 'string' },
      domain: { type: 'string' },
      webhookUrl: { type: 'string', nullable: true },
      webhookName: { type: 'string', nullable: true },
      active: { type: 'boolean' },
      isVerified: { type: 'boolean' },
      verificationStatus: { type: 'string', nullable: true },
      configuration: {
        type: 'object',
        nullable: true,
        properties: {
          allowAttachments: { type: 'boolean' },
          includeEnvelope: { type: 'boolean' }
        }
      },
      postfix_configured: { type: 'boolean' },
      createdAt: { type: 'string', format: 'date-time' },
      updatedAt: { type: 'string', format: 'date-time' },
      aliases: {
        type: 'array',
        items: {
          type: 'object',
          properties: {
            id: { type: 'string' },
            email: { type: 'string' },
            webhookName: { type: 'string', nullable: true },
            active: { type: 'boolean' }
          }
        }
      },
      lastVerificationAttempt: { type: 'string', format: 'date-time', nullable: true },
      verificationFailureCount: { type: 'integer', nullable: true },
      nextVerificationCheck: { type: 'string', format: 'date-time', nullable: true },
      expectedTxtRecord: { type: 'string', nullable: true }
    }
  },

  DomainDetailResponse: {
    type: 'object',
    properties: {
      id: { type: 'string' },
      domain: { type: 'string' },
      webhookUrl: { type: 'string', nullable: true },
      webhookName: { type: 'string', nullable: true },
      active: { type: 'boolean' },
      isVerified: { type: 'boolean' },
      verificationStatus: { type: 'string', nullable: true },
      configuration: {
        type: 'object',
        nullable: true,
        properties: {
          allowAttachments: { type: 'boolean' },
          includeEnvelope: { type: 'boolean' }
        }
      },
      postfix_configured: { type: 'boolean' },
      createdAt: { type: 'string', format: 'date-time' },
      updatedAt: { type: 'string', format: 'date-time' },
      aliases: {
        type: 'array',
        items: {
          type: 'object',
          properties: {
            id: { type: 'string' },
            email: { type: 'string' },
            webhookName: { type: 'string', nullable: true },
            active: { type: 'boolean' }
          }
        }
      },
      lastVerificationAttempt: { type: 'string', format: 'date-time', nullable: true },
      verificationFailureCount: { type: 'integer', nullable: true },
      nextVerificationCheck: { type: 'string', format: 'date-time', nullable: true },
      expectedTxtRecord: { type: 'string', nullable: true }
    }
  },

  // Request body schemas
  CreateDomainRequest: {
    type: 'object',
    properties: {
      domain: { type: 'string', example: 'example.com' },
      webhookUrl: { type: 'string', format: 'url', example: 'https://your-app.com/webhook' },
      webhookId: { type: 'string', example: 'webhook_123' },
      active: { type: 'boolean', default: true },
      createCatchAll: { 
        type: 'boolean', 
        default: true, 
        description: 'Create a catch-all alias for this domain' 
      },
    },
    required: ['domain'],
    anyOf: [
      { required: ['webhookUrl'] },
      { required: ['webhookId'] }
    ],
  },

  UpdateDomainRequest: {
    type: 'object',
    properties: {
      active: { type: 'boolean', nullable: true },
      webhookId: { type: 'string', nullable: true },
      allowAttachments: { type: 'boolean', nullable: true },
      includeEnvelope: { type: 'boolean', nullable: true }
    },
    minProperties: 1
  },

  UpdateDomainStatusRequest: {
    type: 'object',
    properties: {
      active: { type: 'boolean', description: 'New active status' }
    },
    required: ['active']
  },

  UpdateDomainWebhookRequest: {
    type: 'object',
    properties: {
      webhookId: { type: 'string', description: 'New webhook ID' }
    },
    required: ['webhookId']
  },

  // Success response schemas
  CreateDomainResponse: {
    type: 'object',
    properties: {
      success: { type: 'boolean' },
      domain: {
        type: 'object',
        properties: {
          id: { type: 'string' },
          domain: { type: 'string' },
          apiKey: { type: 'string', nullable: true },
          dkimPublicKey: { type: 'string', nullable: true },
          dkimSelector: { type: 'string', nullable: true },
          isVerified: { type: 'boolean' },
          verificationToken: { type: 'string', nullable: true },
          createdAt: { type: 'string', format: 'date-time' },
          updatedAt: { type: 'string', format: 'date-time' }
        }
      },
      webhook: {
        type: 'object',
        properties: {
          id: { type: 'string' },
          name: { type: 'string' },
          url: { type: 'string' },
          verified: { type: 'boolean' }
        }
      },
      instructions: {
        type: 'object',
        properties: {
          mx_record: {
            type: 'object',
            properties: {
              type: { type: 'string' },
              name: { type: 'string' },
              value: { type: 'string' },
              priority: { type: 'integer' }
            }
          },
          txt_record: {
            type: 'object',
            properties: {
              type: { type: 'string' },
              name: { type: 'string' },
              value: { type: 'string' }
            }
          },
          next_steps: {
            type: 'array',
            items: { type: 'string' }
          }
        }
      },
      postfix_status: { type: 'string' }
    }
  },

  UpdateDomainResponse: {
    type: 'object',
    properties: {
      success: { type: 'boolean' },
      domain: {
        type: 'object',
        properties: {
          id: { type: 'string' },
          domain: { type: 'string' },
          apiKey: { type: 'string', nullable: true },
          dkimPublicKey: { type: 'string', nullable: true },
          dkimSelector: { type: 'string', nullable: true },
          isVerified: { type: 'boolean' },
          verificationToken: { type: 'string', nullable: true },
          createdAt: { type: 'string', format: 'date-time' },
          updatedAt: { type: 'string', format: 'date-time' }
        }
      },
      message: { type: 'string' }
    }
  },

  UpdateDomainStatusResponse: {
    type: 'object',
    properties: {
      success: { type: 'boolean' },
      domain: {
        type: 'object',
        properties: {
          id: { type: 'string' },
          domain: { type: 'string' },
          active: { type: 'boolean' },
          updatedAt: { type: 'string', format: 'date-time' }
        }
      },
      message: { type: 'string' }
    }
  },

  UpdateDomainWebhookResponse: {
    type: 'object',
    properties: {
      success: { type: 'boolean' },
      domain: {
        type: 'object',
        properties: {
          id: { type: 'string' },
          domain: { type: 'string' },
          webhookId: { type: 'string' },
          updatedAt: { type: 'string', format: 'date-time' }
        }
      },
      webhook: {
        type: 'object',
        properties: {
          id: { type: 'string' },
          name: { type: 'string' },
          url: { type: 'string' }
        }
      },
      message: { type: 'string' }
    }
  },

  DomainListResponse: {
    type: 'object',
    properties: {
      domains: {
        type: 'array',
        items: {
          type: 'object',
          properties: {
            id: { type: 'string' },
            domain: { type: 'string' },
            webhook: {
              type: 'object',
              nullable: true,
              properties: {
                id: { type: 'string' },
                name: { type: 'string' },
                url: { type: 'string' },
                verified: { type: 'boolean' }
              }
            },
            active: { type: 'boolean' },
            isVerified: { type: 'boolean' },
            verificationStatus: { type: 'string', nullable: true },
            configuration: {
              type: 'object',
              nullable: true,
              properties: {
                allowAttachments: { type: 'boolean' },
                includeEnvelope: { type: 'boolean' }
              }
            },
            postfix_configured: { type: 'boolean' },
            createdAt: { type: 'string', format: 'date-time' },
            updatedAt: { type: 'string', format: 'date-time' },
            lastVerificationAttempt: { type: 'string', format: 'date-time', nullable: true },
            verificationFailureCount: { type: 'integer', nullable: true },
            nextVerificationCheck: { type: 'string', format: 'date-time', nullable: true },
            expectedTxtRecord: { type: 'string', nullable: true },
            aliases: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  id: { type: 'string' },
                  email: { type: 'string' },
                  webhookName: { type: 'string', nullable: true },
                  active: { type: 'boolean' }
                }
              }
            }
          }
        }
      },
      total: { type: 'integer' },
      postfix_status: { type: 'string' },
      verified_count: { type: 'integer' },
      pending_verification: { type: 'integer' }
    }
  },

  DeleteDomainResponse: {
    type: 'object',
    properties: {
      success: { type: 'boolean' },
      domain: { type: 'string' },
      message: { type: 'string' },
      postfix_status: { type: 'string' }
    }
  },

  VerifyDomainResponse: {
    type: 'object',
    properties: {
      success: { type: 'boolean' },
      domain: { type: 'string' },
      verification: {
        type: 'object',
        properties: {
          verified: { type: 'boolean' },
          status: { type: 'string' },
          timestamp: { type: 'string', format: 'date-time' },
          expectedRecord: { type: 'string', nullable: true },
          foundRecords: { type: 'array', items: { type: 'string' }, nullable: true },
          cached: { type: 'boolean', nullable: true },
          error: { type: 'string', nullable: true }
        }
      },
      domain_status: {
        type: 'object',
        properties: {
          domain: { type: 'string' },
          isVerified: { type: 'boolean' },
          dnsRecords: {
            type: 'array',
            items: {
              type: 'object',
              properties: {
                type: { type: 'string' },
                name: { type: 'string' },
                value: { type: 'string' },
                status: { type: 'string' }
              }
            }
          }
        }
      },
      message: { type: 'string' }
    }
  },

  // Parameter schemas
  DomainNameParam: {
    type: 'object',
    properties: {
      domain: { type: 'string', description: 'The domain name (e.g., example.com)' }
    },
    required: ['domain']
  },

  DomainIdParam: {
    type: 'object',
    properties: {
      domainId: { type: 'string', description: 'The domain ID' }
    },
    required: ['domainId']
  }
};
