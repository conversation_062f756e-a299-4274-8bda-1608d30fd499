import { FastifyRequest, FastifyReply } from 'fastify'
import { prisma } from '../../lib/prisma.js'
import bcrypt from 'bcrypt'
import { logger } from '../../utils/logger.js'

interface UpdateProfileRequest {
  name?: string
}

interface ChangePasswordRequest {
  currentPassword: string
  newPassword: string
}

interface ChangeEmailRequest {
  newEmail: string
  password: string
}

export class ProfileController {
  /**
   * Get current user profile
   */
  async getProfile(request: FastifyRequest, reply: FastifyReply) {
    try {
      const user = (request as any).user
      
      const userProfile = await prisma.user.findUnique({
        where: { id: user.id },
        select: {
          id: true,
          email: true,
          name: true,
          planType: true,
          verified: true,
          monthlyEmailLimit: true,
          currentMonthEmails: true,
          createdAt: true,
          updatedAt: true
        }
      })

      if (!userProfile) {
        return reply.status(404).send({
          statusCode: 404,
          error: 'Not Found',
          message: 'User profile not found'
        })
      }

      return reply.send({
        success: true,
        user: userProfile
      })
    } catch (error: any) {
      logger.error({ err: error }, 'Error getting user profile')
      return reply.status(500).send({
        statusCode: 500,
        error: 'Internal Server Error',
        message: 'Failed to retrieve user profile'
      })
    }
  }

  /**
   * Update user profile (name only for now)
   */
  async updateProfile(request: FastifyRequest, reply: FastifyReply) {
    try {
      const user = (request as any).user
      const { name } = request.body as UpdateProfileRequest

      // Validate input
      if (name !== undefined && (typeof name !== 'string' || name.length > 100)) {
        return reply.status(400).send({
          statusCode: 400,
          error: 'Bad Request',
          message: 'Name must be a string with maximum 100 characters'
        })
      }

      const updatedUser = await prisma.user.update({
        where: { id: user.id },
        data: {
          name: name || null
        },
        select: {
          id: true,
          email: true,
          name: true,
          planType: true,
          verified: true,
          updatedAt: true
        }
      })

      return reply.send({
        success: true,
        message: 'Profile updated successfully',
        user: updatedUser
      })
    } catch (error: any) {
      logger.error({ err: error }, 'Error updating user profile')
      return reply.status(500).send({
        statusCode: 500,
        error: 'Internal Server Error',
        message: 'Failed to update user profile'
      })
    }
  }

  /**
   * Change user password
   */
  async changePassword(request: FastifyRequest, reply: FastifyReply) {
    try {
      const user = (request as any).user
      const { currentPassword, newPassword } = request.body as ChangePasswordRequest

      // Validate input
      if (!currentPassword || !newPassword) {
        return reply.status(400).send({
          statusCode: 400,
          error: 'Bad Request',
          message: 'Current password and new password are required'
        })
      }

      if (newPassword.length < 6) {
        return reply.status(400).send({
          statusCode: 400,
          error: 'Bad Request',
          message: 'New password must be at least 6 characters long'
        })
      }

      // Get current user with password
      const currentUser = await prisma.user.findUnique({
        where: { id: user.id },
        select: { password: true }
      })

      if (!currentUser) {
        return reply.status(404).send({
          statusCode: 404,
          error: 'Not Found',
          message: 'User not found'
        })
      }

      // Verify current password
      const passwordMatch = await bcrypt.compare(currentPassword, currentUser.password)
      if (!passwordMatch) {
        return reply.status(400).send({
          statusCode: 400,
          error: 'Bad Request',
          message: 'Current password is incorrect'
        })
      }

      // Hash new password
      const hashedNewPassword = await bcrypt.hash(newPassword, 10)

      // Update password
      await prisma.user.update({
        where: { id: user.id },
        data: { password: hashedNewPassword }
      })

      return reply.send({
        success: true,
        message: 'Password changed successfully'
      })
    } catch (error: any) {
      logger.error({ err: error }, 'Error changing user password')
      return reply.status(500).send({
        statusCode: 500,
        error: 'Internal Server Error',
        message: 'Failed to change password'
      })
    }
  }

  /**
   * Change user email (requires password confirmation)
   */
  async changeEmail(request: FastifyRequest, reply: FastifyReply) {
    try {
      const user = (request as any).user
      const { newEmail, password } = request.body as ChangeEmailRequest

      // Validate input
      if (!newEmail || !password) {
        return reply.status(400).send({
          statusCode: 400,
          error: 'Bad Request',
          message: 'New email and password are required'
        })
      }

      // Basic email validation
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
      if (!emailRegex.test(newEmail)) {
        return reply.status(400).send({
          statusCode: 400,
          error: 'Bad Request',
          message: 'Invalid email format'
        })
      }

      // Check if email is already taken
      const existingUser = await prisma.user.findUnique({
        where: { email: newEmail }
      })

      if (existingUser && existingUser.id !== user.id) {
        return reply.status(409).send({
          statusCode: 409,
          error: 'Conflict',
          message: 'Email address is already in use'
        })
      }

      // Get current user with password
      const currentUser = await prisma.user.findUnique({
        where: { id: user.id },
        select: { password: true, email: true }
      })

      if (!currentUser) {
        return reply.status(404).send({
          statusCode: 404,
          error: 'Not Found',
          message: 'User not found'
        })
      }

      // Verify password
      const passwordMatch = await bcrypt.compare(password, currentUser.password)
      if (!passwordMatch) {
        return reply.status(400).send({
          statusCode: 400,
          error: 'Bad Request',
          message: 'Password is incorrect'
        })
      }

      // Update email
      const updatedUser = await prisma.user.update({
        where: { id: user.id },
        data: { 
          email: newEmail,
          verified: false // Reset verification status when email changes
        },
        select: {
          id: true,
          email: true,
          name: true,
          verified: true
        }
      })

      return reply.send({
        success: true,
        message: 'Email address changed successfully',
        user: updatedUser
      })
    } catch (error: any) {
      logger.error({ err: error }, 'Error changing user email')
      return reply.status(500).send({
        statusCode: 500,
        error: 'Internal Server Error',
        message: 'Failed to change email address'
      })
    }
  }
}
