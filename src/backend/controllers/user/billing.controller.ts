import { FastifyRequest, FastifyReply } from 'fastify';
import { UserAuthService } from '../../services/auth/user-auth.service.js';
import { PlanConfigService } from '../../services/billing/plan-config.service.js';
import { UsageCalculationService } from '../../services/billing/usage-calculation.service.js';
import { CreditService } from '../../services/billing/credit.service.js';
import { paymentWorkflowService } from '../../services/payment/payment-workflow.service.js';
import { logger } from '../../utils/logger.js';
import { env } from '../../config/env.js';

const userAuthService = new UserAuthService();

export class BillingController {
  /**
   * Get current user's plan information
   */
  async getPlanInfo(request: FastifyRequest, reply: FastifyReply) {
    try {
      const user = (request as any).user;
      
      const planInfo = await userAuthService.getUserPlanInfo(user.id);
      
      if (!planInfo) {
        return reply.code(404).send({
          statusCode: 404,
          error: 'Not Found',
          message: 'User not found'
        });
      }

      return reply.send({
        success: true,
        plan: planInfo.planConfig,
        usage: planInfo.usage,
        limits: planInfo.limits
      });
    } catch (error: any) {
      logger.error({ error: error.message, stack: error.stack }, 'Failed to get plan info');
      return reply.code(500).send({
        statusCode: 500,
        error: 'Internal Server Error',
        message: 'Failed to retrieve plan information'
      });
    }
  }

  /**
   * Get all available plans
   */
  async getAvailablePlans(request: FastifyRequest, reply: FastifyReply) {
    try {
      const plans = PlanConfigService.getAllPlans();
      
      return reply.send({
        success: true,
        plans
      });
    } catch (error: any) {
      logger.error({ error: error.message, stack: error.stack }, 'Failed to get available plans');
      return reply.code(500).send({
        statusCode: 500,
        error: 'Internal Server Error',
        message: 'Failed to retrieve available plans'
      });
    }
  }

  /**
   * Update user's plan
   */
  async updatePlan(request: FastifyRequest, reply: FastifyReply) {
    try {
      const user = (request as any).user;
      const { planType } = request.body as { planType: string };

      if (!planType) {
        return reply.code(400).send({
          statusCode: 400,
          error: 'Bad Request',
          message: 'Plan type is required'
        });
      }

      // Validate plan type exists
      try {
        PlanConfigService.getPlanConfig(planType);
      } catch (error) {
        return reply.code(400).send({
          statusCode: 400,
          error: 'Bad Request',
          message: 'Invalid plan type'
        });
      }

      const result = await userAuthService.updateUserPlan(user.id, planType);

      if (!result.success) {
        return reply.code(400).send({
          statusCode: 400,
          error: 'Bad Request',
          message: result.error
        });
      }

      return reply.send({
        success: true,
        message: 'Plan updated successfully',
        user: result.user
      });
    } catch (error: any) {
      logger.error({ error: error.message, stack: error.stack }, 'Failed to update plan');
      return reply.code(500).send({
        statusCode: 500,
        error: 'Internal Server Error',
        message: 'Failed to update plan'
      });
    }
  }

  /**
   * Get usage statistics for the current user
   */
  async getUsageStats(request: FastifyRequest, reply: FastifyReply) {
    try {
      const user = (request as any).user;
      
      const planInfo = await userAuthService.getUserPlanInfo(user.id);
      
      if (!planInfo) {
        return reply.code(404).send({
          statusCode: 404,
          error: 'Not Found',
          message: 'User not found'
        });
      }

      // Calculate usage percentages
      const emailUsagePercent = Math.round((planInfo.usage.emails / planInfo.limits.emails) * 100);
      const domainUsagePercent = planInfo.limits.domains ? 
        Math.round((planInfo.usage.domains / planInfo.limits.domains) * 100) : 0;
      const webhookUsagePercent = planInfo.limits.webhooks ? 
        Math.round((planInfo.usage.webhooks / planInfo.limits.webhooks) * 100) : 0;
      const aliasUsagePercent = planInfo.limits.aliases ? 
        Math.round((planInfo.usage.aliases / planInfo.limits.aliases) * 100) : 0;

      return reply.send({
        success: true,
        usage: planInfo.usage,
        limits: planInfo.limits,
        percentages: {
          emails: emailUsagePercent,
          domains: domainUsagePercent,
          webhooks: webhookUsagePercent,
          aliases: aliasUsagePercent
        },
        plan: {
          name: planInfo.planConfig.name,
          type: planInfo.planConfig.name.toLowerCase()
        }
      });
    } catch (error: any) {
      logger.error({ error: error.message, stack: error.stack }, 'Failed to get usage stats');
      return reply.code(500).send({
        statusCode: 500,
        error: 'Internal Server Error',
        message: 'Failed to retrieve usage statistics'
      });
    }
  }

  /**
   * Get comprehensive billing information for the user
   */
  async getBillingInfo(request: FastifyRequest, reply: FastifyReply) {
    try {
      const user = (request as any).user;

      // Get enhanced usage information
      const usageInfo = await UsageCalculationService.getUserUsageInfo(user.id);
      const planConfig = PlanConfigService.getPlanConfig(user.planType || 'free');

      // Get real payment history
      const recentPayments = await paymentWorkflowService.getUserPayments(user.id, 5);

      const billingInfo = {
        currentPlan: {
          type: planConfig.name.toLowerCase(),
          name: planConfig.name,
          interval: 'monthly', // Default for now
          amount: planConfig.price ? {
            value: planConfig.price.monthly.toString(),
            currency: planConfig.price.currency
          } : null,
          status: 'active'
        },
        paymentMethods: [], // TODO: Implement payment method retrieval
        recentPayments: recentPayments,
        usage: usageInfo.currentUsage,
        limits: usageInfo.limits,
        // Enhanced billing data
        emailBreakdown: usageInfo.emailBreakdown,
        creditInfo: usageInfo.creditInfo
      };

      return reply.send({
        success: true,
        data: billingInfo
      });
    } catch (error: any) {
      logger.error({ error: error.message, stack: error.stack }, 'Failed to get billing info');
      return reply.code(500).send({
        statusCode: 500,
        error: 'Internal Server Error',
        message: 'Failed to retrieve billing information'
      });
    }
  }

  /**
   * Create a payment for plan upgrade
   */
  async createPayment(request: FastifyRequest, reply: FastifyReply) {
    try {
      const user = (request as any).user;
      const { planType, interval, successUrl, cancelUrl } = request.body as {
        planType: 'pro' | 'enterprise';
        interval: 'monthly' | 'yearly';
        successUrl?: string;
        cancelUrl?: string;
      };

      // Validate input
      if (!planType || !interval) {
        return reply.code(400).send({
          statusCode: 400,
          error: 'Bad Request',
          message: 'planType and interval are required'
        });
      }

      if (!['pro', 'enterprise'].includes(planType)) {
        return reply.code(400).send({
          statusCode: 400,
          error: 'Bad Request',
          message: 'Invalid plan type'
        });
      }

      if (!['monthly', 'yearly'].includes(interval)) {
        return reply.code(400).send({
          statusCode: 400,
          error: 'Bad Request',
          message: 'Invalid interval'
        });
      }

      // Create payment
      const payment = await paymentWorkflowService.createPayment({
        userId: user.id,
        planType,
        interval,
        successUrl: successUrl || `${env.NODE_ENV === 'development' ? 'http://localhost:3000' : 'https://emailconnect.eu'}/settings?payment=success`,
        cancelUrl: cancelUrl || `${env.NODE_ENV === 'development' ? 'http://localhost:3000' : 'https://emailconnect.eu'}/settings?payment=cancelled`
      });

      return reply.send({
        success: true,
        payment: {
          id: payment.paymentId,
          mollieId: payment.mollieId,
          checkoutUrl: payment.checkoutUrl,
          amount: payment.amount
        }
      });

    } catch (error: any) {
      logger.error({
        error: error.message,
        stack: error.stack,
        userId: (request as any).user?.id
      }, 'Failed to create payment');

      return reply.code(500).send({
        statusCode: 500,
        error: 'Internal Server Error',
        message: 'Failed to create payment'
      });
    }
  }

  /**
   * Purchase email credits
   */
  async purchaseCredits(request: FastifyRequest, reply: FastifyReply) {
    try {
      const user = (request as any).user;
      const { creditAmount, successUrl, cancelUrl } = request.body as {
        creditAmount: number;
        successUrl?: string;
        cancelUrl?: string;
      };

      // Validate input
      if (!creditAmount || creditAmount <= 0) {
        return reply.code(400).send({
          statusCode: 400,
          error: 'Bad Request',
          message: 'creditAmount must be a positive number'
        });
      }

      // Validate credit amount (must be multiple of 100)
      if (creditAmount % 100 !== 0) {
        return reply.code(400).send({
          statusCode: 400,
          error: 'Bad Request',
          message: 'Credit amount must be a multiple of 100'
        });
      }

      // Get credit pricing for user's plan
      const pricing = await CreditService.getCreditPricing(user.id);
      const totalAmount = (creditAmount / 100) * pricing.pricePerHundred;

      // Create one-time payment for credits
      const payment = await paymentWorkflowService.createPayment({
        userId: user.id,
        planType: 'credits', // Special type for credit purchases
        interval: 'one-time',
        amount: totalAmount,
        currency: pricing.currency,
        description: `Purchase ${creditAmount} email credits`,
        successUrl: successUrl || `${env.NODE_ENV === 'development' ? 'http://localhost:3000' : 'https://emailconnect.eu'}/settings?credits=success`,
        cancelUrl: cancelUrl || `${env.NODE_ENV === 'development' ? 'http://localhost:3000' : 'https://emailconnect.eu'}/settings?credits=cancelled`,
        metadata: {
          type: 'credit_purchase',
          creditAmount,
          pricePerHundred: pricing.pricePerHundred
        }
      });

      return reply.send({
        success: true,
        payment: {
          id: payment.paymentId,
          mollieId: payment.mollieId,
          checkoutUrl: payment.checkoutUrl,
          amount: payment.amount,
          creditAmount,
          pricePerHundred: pricing.pricePerHundred
        }
      });

    } catch (error: any) {
      logger.error({
        error: error.message,
        stack: error.stack,
        userId: (request as any).user?.id
      }, 'Failed to create credit purchase');

      return reply.code(500).send({
        statusCode: 500,
        error: 'Internal Server Error',
        message: 'Failed to create credit purchase'
      });
    }
  }

  /**
   * Check if user can perform an action based on their plan limits
   */
  async checkLimits(request: FastifyRequest, reply: FastifyReply) {
    try {
      const user = (request as any).user;
      const { action } = request.query as { action: string };

      if (!action) {
        return reply.code(400).send({
          statusCode: 400,
          error: 'Bad Request',
          message: 'Action parameter is required'
        });
      }

      const planInfo = await userAuthService.getUserPlanInfo(user.id);
      
      if (!planInfo) {
        return reply.code(404).send({
          statusCode: 404,
          error: 'Not Found',
          message: 'User not found'
        });
      }

      let canPerform = false;
      let reason = '';

      switch (action) {
        case 'send_email':
          canPerform = planInfo.usage.emails < planInfo.limits.emails;
          reason = canPerform ? '' : 'Monthly email limit reached';
          break;
        case 'create_domain':
          canPerform = !planInfo.limits.domains || planInfo.usage.domains < planInfo.limits.domains;
          reason = canPerform ? '' : 'Domain limit reached for your plan';
          break;
        case 'create_webhook':
          canPerform = !planInfo.limits.webhooks || planInfo.usage.webhooks < planInfo.limits.webhooks;
          reason = canPerform ? '' : 'Webhook limit reached for your plan';
          break;
        case 'create_alias':
          canPerform = !planInfo.limits.aliases || planInfo.usage.aliases < planInfo.limits.aliases;
          reason = canPerform ? '' : 'Alias limit reached for your plan';
          break;
        default:
          return reply.code(400).send({
            statusCode: 400,
            error: 'Bad Request',
            message: 'Invalid action'
          });
      }

      return reply.send({
        success: true,
        canPerform,
        reason,
        usage: planInfo.usage,
        limits: planInfo.limits
      });
    } catch (error: any) {
      logger.error({ error: error.message, stack: error.stack }, 'Failed to check limits');
      return reply.code(500).send({
        statusCode: 500,
        error: 'Internal Server Error',
        message: 'Failed to check limits'
      });
    }
  }
}
