import { z } from 'zod';
import dotenv from 'dotenv';

// Load environment variables from .env file
dotenv.config();

const envSchema = z.object({
  NODE_ENV: z.enum(['development', 'production', 'test']).default('development'),
  PORT: z.coerce.number().default(3000),
  HOST: z.string().default('0.0.0.0'),
  
  // Database
  DB_USER: z.string().default('postgres'),
  DB_PASSWORD: z.string().default('password'),
  DB_NAME: z.string().default('eu_email_webhook'),
  DATABASE_URL: z.string().default('postgresql://user:password@localhost:5432/eu_email_webhook'),
  
  // Redis
  REDIS_URL: z.string().default('redis://localhost:6379'),
  
  // Postfix Manager Service
  POSTFIX_MANAGER_URL: z.string().default('http://localhost:3001'),
  
  // User authentication
  USER_JWT_SECRET: z.string().min(1, { message: "USER_JWT_SECRET must be set and not empty" }),
  USER_JWT_EXPIRES_IN: z.string().min(1, { message: "USER_JWT_EXPIRES_IN must be set and not empty" }).default('7d'),

  // Admin authentication
  ADMIN_USERNAME: z.string().default('admin'),
  ADMIN_PASSWORD: z.string().min(1, { message: "ADMIN_PASSWORD must be set and not empty" }),
  ADMIN_JWT_SECRET: z.string().min(1, { message: "ADMIN_JWT_SECRET must be set and not empty" }),
  ADMIN_JWT_EXPIRES_IN: z.string().min(1, { message: "ADMIN_JWT_EXPIRES_IN must be set and not empty" }).default('1h'),
  
  // Email processing
  MAX_EMAIL_SIZE_MB: z.coerce.number().default(25),
  WEBHOOK_TIMEOUT_MS: z.coerce.number().default(30000),
  WEBHOOK_RETRY_ATTEMPTS: z.coerce.number().default(3),
  
  // DNS verification
  DNS_VERIFICATION_TIMEOUT_MS: z.coerce.number().default(5000),
  DNS_VERIFICATION_CACHE_TTL_MS: z.coerce.number().default(300000), // 5 minutes
  DNS_VERIFICATION_RETRY_ATTEMPTS: z.coerce.number().default(3),
  
  // GDPR compliance
  EMAIL_RETENTION_DAYS: z.coerce.number().default(30),
  LOG_RETENTION_DAYS: z.coerce.number().default(90),

  // Usage tracking and billing
  DEFAULT_MONTHLY_EMAIL_LIMIT: z.coerce.number().default(50),
  FREE_PLAN_EMAIL_LIMIT: z.coerce.number().default(50),
  PRO_PLAN_EMAIL_LIMIT: z.coerce.number().default(1000),
  ENTERPRISE_PLAN_EMAIL_LIMIT: z.coerce.number().default(10000),

  // Payment processing (Mollie)
  MOLLIE_API_KEY: z.string().optional(),
  MOLLIE_WEBHOOK_SECRET: z.string().optional(),
  MOLLIE_WEBHOOK_URL: z.string().optional(),
  MOLLIE_TEST_MODE: z.coerce.boolean().default(true),

  // WebSocket configuration
  WEBSOCKET_ALLOWED_ORIGINS: z.string().optional(),

  // Attachment processing
  MAX_INLINE_ATTACHMENT_SIZE_KB: z.coerce.number().default(128),
  DEFAULT_ATTACHMENT_RETENTION_HOURS: z.coerce.number().default(1),
  PAID_ATTACHMENT_RETENTION_HOURS: z.coerce.number().default(24),
});

export const env = envSchema.parse(process.env);

export type Env = z.infer<typeof envSchema>;
