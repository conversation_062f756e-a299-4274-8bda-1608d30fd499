import { prisma } from '../../lib/prisma.js';
import { WebhookService } from './webhook.service.js';
import { AliasService } from './alias.service.js';
import { DomainService } from './domain.service.js';
import { WebhookSyncService } from './webhook-sync.service.js';

interface CreateWebhookAliasData {
  domainId: string;
  webhookUrl: string;
  webhookName: string;
  webhookDescription?: string;
  aliasType: 'catchall' | 'specific';
  localPart?: string;
  syncWithDomain?: boolean;
  autoVerify?: boolean;
  userId: string;
}

interface WebhookAliasResult {
  success: boolean;
  webhook?: {
    id: string;
    name: string;
    url: string;
    verified: boolean;
    verificationToken?: string;
    createdAt: string;
  };
  alias?: {
    id: string;
    email: string;
    active: boolean;
    createdAt: string;
  };
  domain?: {
    id: string;
    domain: string;
    webhookUpdated: boolean;
  };
  message?: string;
  error?: string;
}

export class WebhookAliasService {
  private webhookService: WebhookService;
  private aliasService: AliasService;
  private domainService: DomainService;
  private webhookSyncService: WebhookSyncService;

  constructor() {
    this.webhookService = new WebhookService();
    this.aliasService = new AliasService();
    this.domainService = new DomainService();
    this.webhookSyncService = new WebhookSyncService();
  }

  async createWebhookAlias(data: CreateWebhookAliasData): Promise<WebhookAliasResult> {
    // Start a transaction to ensure atomicity
    return await prisma.$transaction(async (tx) => {
      try {
        // 1. Validate domain exists and user has access
        const domain = await tx.domain.findFirst({
          where: {
            id: data.domainId,
            userId: data.userId
          }
        });

        if (!domain) {
          throw new Error('Domain not found or access denied');
        }

        // 2. Construct email address based on alias type
        let emailAddress: string;
        if (data.aliasType === 'catchall') {
          emailAddress = `*@${domain.domain}`;
        } else {
          if (!data.localPart) {
            throw new Error('localPart is required for specific aliases');
          }
          emailAddress = `${data.localPart}@${domain.domain}`;
        }

        // 3. Check if alias already exists
        const existingAlias = await tx.alias.findFirst({
          where: {
            email: emailAddress,
            domainId: data.domainId
          }
        });

        // For catch-all aliases, update existing instead of failing
        if (existingAlias && data.aliasType === 'catchall') {
          console.log(`Updating existing catch-all alias ${emailAddress} with new webhook`);

          // Create new webhook first
          const webhookResult = await this.webhookService.createWebhook({
            name: data.webhookName,
            url: data.webhookUrl,
            description: data.webhookDescription,
            userId: data.userId
          });

          if (!webhookResult.webhook) {
            throw new Error('Failed to create webhook');
          }

          // Auto-verify webhook if requested
          if (data.autoVerify) {
            const updatedWebhook = await tx.webhook.update({
              where: { id: webhookResult.webhook.id },
              data: { verified: true }
            });
            webhookResult.webhook.verified = true;
          }

          // Update existing alias with new webhook
          const updatedAlias = await tx.alias.update({
            where: { id: existingAlias.id },
            data: {
              webhookId: webhookResult.webhook.id,
              active: webhookResult.webhook.verified
            }
          });

          // Handle domain synchronization for catch-all
          let domainInfo: { id: string; domain: string; webhookUpdated: boolean } | undefined;
          if (data.syncWithDomain !== false) {
            try {
              // Update domain to use the same webhook
              await tx.domain.update({
                where: { id: data.domainId },
                data: { webhookId: webhookResult.webhook.id }
              });

              // Update or create webhook sync relationship
              await tx.webhookSync.upsert({
                where: {
                  domainId_catchAllAliasId: {
                    domainId: data.domainId,
                    catchAllAliasId: existingAlias.id
                  }
                },
                update: {
                  webhookId: webhookResult.webhook.id,
                  syncEnabled: true
                },
                create: {
                  domainId: data.domainId,
                  catchAllAliasId: existingAlias.id,
                  webhookId: webhookResult.webhook.id,
                  syncEnabled: true
                }
              });

              domainInfo = {
                id: domain.id,
                domain: domain.domain,
                webhookUpdated: true
              };
            } catch (error) {
              console.warn(`Failed to sync webhook with domain ${domain.domain}:`, error);
              domainInfo = {
                id: domain.id,
                domain: domain.domain,
                webhookUpdated: false
              };
            }
          }

          const message = `Updated catch-all alias ${emailAddress} with webhook ${data.webhookName}${domainInfo?.webhookUpdated ? ' and synced with domain' : ''}`;

          return {
            success: true,
            webhook: {
              id: webhookResult.webhook.id,
              name: webhookResult.webhook.name,
              url: webhookResult.webhook.url,
              verified: webhookResult.webhook.verified,
              verificationToken: webhookResult.webhook.verified ? undefined : webhookResult.webhook.id.slice(-5),
              createdAt: webhookResult.webhook.createdAt
            },
            alias: {
              id: updatedAlias.id,
              email: updatedAlias.email,
              active: updatedAlias.active,
              createdAt: updatedAlias.createdAt.toISOString()
            },
            domain: domainInfo,
            message
          };
        }

        // For non-catch-all aliases, still fail if exists
        if (existingAlias) {
          throw new Error(`Alias ${emailAddress} already exists`);
        }

        // 4. Create webhook first
        const webhookResult = await this.webhookService.createWebhook({
          name: data.webhookName,
          url: data.webhookUrl,
          description: data.webhookDescription,
          userId: data.userId
        });

        if (!webhookResult.webhook) {
          throw new Error('Failed to create webhook');
        }

        // 5. Auto-verify webhook if requested
        if (data.autoVerify) {
          // For auto-verification, mark webhook as verified directly
          // This is appropriate for automated use cases like n8n where
          // the system creating the webhook controls the destination URL
          const updatedWebhook = await tx.webhook.update({
            where: { id: webhookResult.webhook.id },
            data: { verified: true }
          });

          webhookResult.webhook.verified = true;
        }

        // 6. Create alias directly in transaction context
        // This ensures we see the updated webhook verification status
        const alias = await tx.alias.create({
          data: {
            email: emailAddress,
            domainId: data.domainId,
            webhookId: webhookResult.webhook.id,
            active: webhookResult.webhook.verified // Set active based on webhook verification status
          }
        });

        const aliasResult = {
          success: true,
          alias: {
            id: alias.id,
            email: alias.email,
            active: alias.active,
            createdAt: alias.createdAt.toISOString(),
            updatedAt: alias.updatedAt.toISOString(),
            domainId: alias.domainId,
            webhookId: alias.webhookId
          }
        };

        // 7. Handle domain webhook synchronization for catch-all aliases
        let domainInfo: { id: string; domain: string; webhookUpdated: boolean } | undefined;

        if (data.aliasType === 'catchall' && data.syncWithDomain !== false) {
          try {
            // Update domain to use the same webhook
            await tx.domain.update({
              where: { id: data.domainId },
              data: { webhookId: webhookResult.webhook.id }
            });

            // Create webhook sync relationship
            await tx.webhookSync.create({
              data: {
                domainId: data.domainId,
                catchAllAliasId: aliasResult.alias.id,
                webhookId: webhookResult.webhook.id,
                syncEnabled: true
              }
            });

            domainInfo = {
              id: domain.id,
              domain: domain.domain,
              webhookUpdated: true
            };
          } catch (error) {
            // Don't fail the entire operation if domain sync fails
            console.warn(`Failed to sync webhook with domain ${domain.domain}:`, error);
            domainInfo = {
              id: domain.id,
              domain: domain.domain,
              webhookUpdated: false
            };
          }
        }

        // 8. Construct success response
        const message = data.aliasType === 'catchall' 
          ? `Created catch-all alias ${emailAddress} with webhook ${data.webhookName}${domainInfo?.webhookUpdated ? ' and synced with domain' : ''}`
          : `Created alias ${emailAddress} with webhook ${data.webhookName}`;

        return {
          success: true,
          webhook: {
            id: webhookResult.webhook.id,
            name: webhookResult.webhook.name,
            url: webhookResult.webhook.url,
            verified: webhookResult.webhook.verified,
            verificationToken: webhookResult.webhook.verified ? undefined : webhookResult.webhook.id.slice(-5),
            createdAt: webhookResult.webhook.createdAt
          },
          alias: {
            id: aliasResult.alias.id,
            email: aliasResult.alias.email,
            active: aliasResult.alias.active,
            createdAt: aliasResult.alias.createdAt
          },
          domain: domainInfo,
          message
        };

      } catch (error) {
        console.error('Error in createWebhookAlias:', error);
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error occurred'
        };
      }
    });
  }

  // Helper method to validate request data
  validateCreateRequest(data: any): { valid: boolean; error?: string } {
    if (!data.domainId) {
      return { valid: false, error: 'domainId is required' };
    }

    if (!data.webhookUrl) {
      return { valid: false, error: 'webhookUrl is required' };
    }

    if (!data.webhookName) {
      return { valid: false, error: 'webhookName is required' };
    }

    if (!data.aliasType || !['catchall', 'specific'].includes(data.aliasType)) {
      return { valid: false, error: 'aliasType must be either "catchall" or "specific"' };
    }

    if (data.aliasType === 'specific' && !data.localPart) {
      return { valid: false, error: 'localPart is required when aliasType is "specific"' };
    }

    // Validate URL format
    try {
      new URL(data.webhookUrl);
    } catch {
      return { valid: false, error: 'webhookUrl must be a valid URL' };
    }

    return { valid: true };
  }
}
