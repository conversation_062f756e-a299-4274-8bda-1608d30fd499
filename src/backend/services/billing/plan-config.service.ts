import { env } from '../../config/env.js';

export interface PlanConfig {
  name: string;
  monthlyEmailLimit: number;
  features: string[];
  price?: {
    monthly: number;
    yearly: number;
    currency: string;
  };
}

export interface PlanLimits {
  monthlyEmails: number;
  domains?: number;
  webhooks?: number;
  aliases?: number;
}

export interface CreditPricing {
  pricePerHundred: number; // Price in EUR for 100 email credits
  currency: string;
}

export class PlanConfigService {
  private static readonly PLAN_CONFIGS: Record<string, PlanConfig> = {
    free: {
      name: 'Free',
      monthlyEmailLimit: 50,
      features: [
        'Up to 50 emails per month',
        '1 domain',
        '3 aliases per domain',
        '3 webhooks per domain',
        'Basic webhook delivery',
        'Email forwarding',
        'Community support'
      ]
    },
    pro: {
      name: 'Pro',
      monthlyEmailLimit: 1000,
      features: [
        'Up to 1,000 emails per month',
        '5 domains',
        '10 aliases per domain',
        '10 webhooks per domain',
        'Priority webhook delivery',
        'Advanced email routing',
        'Email analytics',
        'Priority support',
        'Discounted credit purchases (€0.80/100 emails)'
      ],
      price: {
        monthly: 9.95,
        yearly: 99.50,
        currency: 'EUR'
      }
    }
  };

  /**
   * Get configuration for a specific plan
   */
  static getPlanConfig(planType: string): PlanConfig {
    const config = this.PLAN_CONFIGS[planType];
    if (!config) {
      throw new Error(`Unknown plan type: ${planType}`);
    }
    return config;
  }

  /**
   * Get all available plans
   */
  static getAllPlans(): Record<string, PlanConfig> {
    return { ...this.PLAN_CONFIGS };
  }

  /**
   * Get plan limits for a specific plan type
   */
  static getPlanLimits(planType: string, domainCount: number = 1): PlanLimits {
    const config = this.getPlanConfig(planType);

    if (planType === 'free') {
      return {
        monthlyEmails: config.monthlyEmailLimit,
        domains: 1,
        webhooks: 3,
        aliases: 3
      };
    }

    if (planType === 'pro') {
      return {
        monthlyEmails: config.monthlyEmailLimit,
        domains: 5,
        webhooks: domainCount * 10, // 10 webhooks per domain
        aliases: domainCount * 10   // 10 aliases per domain
      };
    }

    // Default fallback (shouldn't reach here with current plans)
    return {
      monthlyEmails: config.monthlyEmailLimit,
      domains: 1,
      webhooks: 1,
      aliases: 1
    };
  }

  /**
   * Check if a plan upgrade is valid
   */
  static isValidUpgrade(currentPlan: string, targetPlan: string): boolean {
    const planHierarchy = ['free', 'pro', 'enterprise'];
    const currentIndex = planHierarchy.indexOf(currentPlan);
    const targetIndex = planHierarchy.indexOf(targetPlan);
    
    return currentIndex !== -1 && targetIndex !== -1 && targetIndex > currentIndex;
  }

  /**
   * Check if a plan downgrade is valid
   */
  static isValidDowngrade(currentPlan: string, targetPlan: string): boolean {
    const planHierarchy = ['free', 'pro', 'enterprise'];
    const currentIndex = planHierarchy.indexOf(currentPlan);
    const targetIndex = planHierarchy.indexOf(targetPlan);
    
    return currentIndex !== -1 && targetIndex !== -1 && targetIndex < currentIndex;
  }

  /**
   * Get the default monthly email limit for new users
   */
  static getDefaultEmailLimit(): number {
    return env.DEFAULT_MONTHLY_EMAIL_LIMIT;
  }

  /**
   * Validate if current usage fits within target plan limits
   */
  static validateUsageForPlan(
    planType: string, 
    currentUsage: { 
      domains: number; 
      webhooks: number; 
      aliases: number; 
    }
  ): { valid: boolean; violations: string[] } {
    const limits = this.getPlanLimits(planType);
    const violations: string[] = [];

    if (currentUsage.domains > (limits.domains || Infinity)) {
      violations.push(`Too many domains (${currentUsage.domains}/${limits.domains})`);
    }
    
    if (currentUsage.webhooks > (limits.webhooks || Infinity)) {
      violations.push(`Too many webhooks (${currentUsage.webhooks}/${limits.webhooks})`);
    }
    
    if (currentUsage.aliases > (limits.aliases || Infinity)) {
      violations.push(`Too many aliases (${currentUsage.aliases}/${limits.aliases})`);
    }

    return {
      valid: violations.length === 0,
      violations
    };
  }
}
