import { FastifyInstance } from 'fastify';
import { Server as SocketIOServer } from 'socket.io';
import { logger } from '../utils/logger.js';
import { UserAuthService } from './auth/user-auth.service.js';

interface AuthenticatedSocket {
  userId: string;
  email: string;
}

interface EmailProcessedEvent {
  type: 'email_processed';
  data: {
    messageId: string;
    fromAddress: string;
    subject: string;
    isTestWebhook: boolean;
    deliveryStatus: 'DELIVERED' | 'FAILED';
    webhookPayload?: any;
    timestamp: string;
  };
}

interface MetricsUpdatedEvent {
  type: 'metrics_updated';
  data: {
    domains: number;
    aliases: number;
    webhooks: number;
    emails: number;
  };
}

type WebSocketEvent = EmailProcessedEvent | MetricsUpdatedEvent;

class WebSocketService {
  private io: SocketIOServer | null = null;
  private userSockets = new Map<string, Set<string>>(); // userId -> Set of socketIds
  private userAuthService: UserAuthService;

  constructor() {
    this.userAuthService = new UserAuthService();
  }

  initialize(fastify: FastifyInstance) {
    // Configure CORS origins based on environment
    const allowedOrigins = process.env.NODE_ENV === 'development'
      ? ["http://localhost:3000", "http://localhost:5173"]
      : process.env.WEBSOCKET_ALLOWED_ORIGINS
        ? process.env.WEBSOCKET_ALLOWED_ORIGINS.split(',')
        : ["https://emailconnect.eu"];

    this.io = new SocketIOServer(fastify.server, {
      cors: {
        origin: allowedOrigins,
        credentials: true
      },
      transports: ['websocket', 'polling'],
      // Add production-specific configuration
      pingTimeout: 60000,
      pingInterval: 25000,
      upgradeTimeout: 30000,
      allowEIO3: true
    });

    this.io.use(async (socket, next) => {
      try {
        // Extract token from httpOnly cookie (secure authentication)
        let token: string | undefined;

        if (socket.handshake.headers.cookie) {
          const cookies = socket.handshake.headers.cookie.split('; ');
          const userTokenCookie = cookies.find(cookie => cookie.startsWith('user_token='));
          if (userTokenCookie) {
            token = userTokenCookie.split('=')[1];
            logger.debug('WebSocket authenticated via secure cookie');
          }
        }

        if (!token) {
          logger.warn('No authentication token found in WebSocket cookies');
          return next(new Error('Authentication required'));
        }

        const verifyResult = this.userAuthService.verifyToken(token);
        if (!verifyResult.success || !verifyResult.payload) {
          return next(new Error('Invalid authentication token'));
        }

        // Attach user info to socket
        (socket as any).user = {
          userId: verifyResult.payload.userId,
          email: verifyResult.payload.email
        };

        next();
      } catch (error) {
        logger.error({ error: error.message }, 'WebSocket authentication failed');
        next(new Error('Authentication failed'));
      }
    });

    this.io.on('connection', (socket) => {
      const user = (socket as any).user as AuthenticatedSocket;
      
      logger.info({ 
        userId: user.userId, 
        socketId: socket.id 
      }, 'User connected to WebSocket');

      // Track user socket
      if (!this.userSockets.has(user.userId)) {
        this.userSockets.set(user.userId, new Set());
      }
      this.userSockets.get(user.userId)!.add(socket.id);

      // Join user-specific room
      socket.join(`user:${user.userId}`);

      // Handle disconnection
      socket.on('disconnect', () => {
        logger.info({ 
          userId: user.userId, 
          socketId: socket.id 
        }, 'User disconnected from WebSocket');

        // Remove socket from tracking
        const userSocketSet = this.userSockets.get(user.userId);
        if (userSocketSet) {
          userSocketSet.delete(socket.id);
          if (userSocketSet.size === 0) {
            this.userSockets.delete(user.userId);
          }
        }
      });

      // Handle ping/pong for connection health
      socket.on('ping', () => {
        socket.emit('pong');
      });
    });

    logger.info('WebSocket service initialized');
  }

  /**
   * Emit event to specific user
   */
  emitToUser(userId: string, event: WebSocketEvent) {
    if (!this.io) {
      // In tests, WebSocket might not be initialized - that's OK
      if (process.env.NODE_ENV !== 'test') {
        logger.warn('WebSocket service not initialized');
      }
      return;
    }

    this.io.to(`user:${userId}`).emit(event.type, event.data);
    
    logger.debug({ 
      userId, 
      eventType: event.type,
      connectedSockets: this.userSockets.get(userId)?.size || 0
    }, 'Event emitted to user');
  }

  /**
   * Emit email processed event
   */
  emitEmailProcessed(userId: string, emailData: EmailProcessedEvent['data']) {
    this.emitToUser(userId, {
      type: 'email_processed',
      data: emailData
    });
  }

  /**
   * Emit metrics updated event
   */
  emitMetricsUpdated(userId: string, metrics: MetricsUpdatedEvent['data']) {
    this.emitToUser(userId, {
      type: 'metrics_updated',
      data: metrics
    });
  }

  /**
   * Get connected users count
   */
  getConnectedUsersCount(): number {
    return this.userSockets.size;
  }

  /**
   * Get user connection status
   */
  isUserConnected(userId: string): boolean {
    return this.userSockets.has(userId) && this.userSockets.get(userId)!.size > 0;
  }

  /**
   * Broadcast to all connected users (admin use)
   */
  broadcast(event: WebSocketEvent) {
    if (!this.io) {
      // In tests, WebSocket might not be initialized - that's OK
      if (process.env.NODE_ENV !== 'test') {
        logger.warn('WebSocket service not initialized');
      }
      return;
    }

    this.io.emit(event.type, event.data);
    logger.debug({ eventType: event.type }, 'Event broadcasted to all users');
  }
}

export const webSocketService = new WebSocketService();
