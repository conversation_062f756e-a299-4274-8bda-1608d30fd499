import { FastifyInstance } from 'fastify'
import { userOrApi<PERSON>eyAuthMiddleware } from '../lib/auth.js'
import { ProfileController } from '../controllers/user/profile.controller.js'

export async function profileRoutes(fastify: FastifyInstance) {
  const profileController = new ProfileController()

  // Get current user profile
  fastify.get('/profile', {
    preHandler: [userOrApiKeyAuthMiddleware],
    schema: {
      tags: ['User Profile'],
      summary: 'Get current user profile',
      description: 'Retrieve the current user\'s profile information',
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            user: {
              type: 'object',
              properties: {
                id: { type: 'string' },
                email: { type: 'string' },
                name: { type: 'string', nullable: true },
                planType: { type: 'string' },
                verified: { type: 'boolean' },
                monthlyEmailLimit: { type: 'integer' },
                currentMonthEmails: { type: 'integer' },
                createdAt: { type: 'string', format: 'date-time' },
                updatedAt: { type: 'string', format: 'date-time' }
              }
            }
          }
        },
        404: { $ref: 'ErrorResponse#' },
        500: { $ref: 'ErrorResponse#' }
      }
    }
  }, profileController.getProfile)

  // Update user profile
  fastify.put('/profile', {
    preHandler: [userOrApiKeyAuthMiddleware],
    schema: {
      tags: ['User Profile'],
      summary: 'Update user profile',
      description: 'Update the current user\'s profile information',
      body: {
        type: 'object',
        properties: {
          name: { 
            type: 'string', 
            nullable: true,
            maxLength: 100,
            description: 'User display name'
          }
        }
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            message: { type: 'string' },
            user: {
              type: 'object',
              properties: {
                id: { type: 'string' },
                email: { type: 'string' },
                name: { type: 'string', nullable: true },
                planType: { type: 'string' },
                verified: { type: 'boolean' },
                updatedAt: { type: 'string', format: 'date-time' }
              }
            }
          }
        },
        400: { $ref: 'ErrorResponse#' },
        500: { $ref: 'ErrorResponse#' }
      }
    }
  }, profileController.updateProfile)

  // Change password
  fastify.put('/profile/password', {
    preHandler: [userOrApiKeyAuthMiddleware],
    schema: {
      tags: ['User Profile'],
      summary: 'Change user password',
      description: 'Change the current user\'s password',
      body: {
        type: 'object',
        required: ['currentPassword', 'newPassword'],
        properties: {
          currentPassword: { 
            type: 'string',
            description: 'Current password for verification'
          },
          newPassword: { 
            type: 'string',
            minLength: 6,
            description: 'New password (minimum 6 characters)'
          }
        }
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            message: { type: 'string' }
          }
        },
        400: { $ref: 'ErrorResponse#' },
        404: { $ref: 'ErrorResponse#' },
        500: { $ref: 'ErrorResponse#' }
      }
    }
  }, profileController.changePassword)

  // Change email
  fastify.put('/profile/email', {
    preHandler: [userOrApiKeyAuthMiddleware],
    schema: {
      tags: ['User Profile'],
      summary: 'Change user email',
      description: 'Change the current user\'s email address',
      body: {
        type: 'object',
        required: ['newEmail', 'password'],
        properties: {
          newEmail: { 
            type: 'string',
            format: 'email',
            description: 'New email address'
          },
          password: { 
            type: 'string',
            description: 'Current password for verification'
          }
        }
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            message: { type: 'string' },
            user: {
              type: 'object',
              properties: {
                id: { type: 'string' },
                email: { type: 'string' },
                name: { type: 'string', nullable: true },
                verified: { type: 'boolean' }
              }
            }
          }
        },
        400: { $ref: 'ErrorResponse#' },
        404: { $ref: 'ErrorResponse#' },
        409: { $ref: 'ErrorResponse#' },
        500: { $ref: 'ErrorResponse#' }
      }
    }
  }, profileController.changeEmail)
}
