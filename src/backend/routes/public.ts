import { FastifyPluginAsync } from 'fastify';
import { PlanConfigService } from '../services/billing/plan-config.service.js';

export const publicRoutes: FastifyPluginAsync = async (fastify) => {
  // Get public plan configurations for pricing display
  fastify.get('/plans', {
    schema: {
      tags: ['Public'],
      summary: 'Get public plan configurations',
      description: 'Retrieve plan configurations for public pricing display',
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            plans: {
              type: 'object',
              properties: {
                free: {
                  type: 'object',
                  properties: {
                    name: { type: 'string' },
                    monthlyEmailLimit: { type: 'integer' },
                    domains: { type: 'integer' },
                    aliases: { type: 'integer' },
                    webhooks: { type: 'integer' },
                    features: {
                      type: 'array',
                      items: { type: 'string' }
                    }
                  }
                },
                pro: {
                  type: 'object',
                  properties: {
                    name: { type: 'string' },
                    monthlyEmailLimit: { type: 'integer' },
                    domains: { type: 'integer' },
                    aliases: { type: 'integer' },
                    webhooks: { type: 'integer' },
                    features: {
                      type: 'array',
                      items: { type: 'string' }
                    },
                    price: {
                      type: 'object',
                      properties: {
                        monthly: { type: 'number' },
                        yearly: { type: 'number' },
                        currency: { type: 'string' }
                      }
                    }
                  }
                }
              }
            },
            creditPricing: {
              type: 'object',
              properties: {
                free: {
                  type: 'object',
                  properties: {
                    pricePerHundred: { type: 'number' },
                    currency: { type: 'string' }
                  }
                },
                pro: {
                  type: 'object',
                  properties: {
                    pricePerHundred: { type: 'number' },
                    currency: { type: 'string' }
                  }
                }
              }
            }
          }
        },
        500: { $ref: 'ErrorResponse#' }
      }
    }
  }, async (request, reply) => {
    try {
      const freePlan = PlanConfigService.getPlanConfig('free');
      const proPlan = PlanConfigService.getPlanConfig('pro');
      const freeCreditPricing = PlanConfigService.getCreditPricing('free');
      const proCreditPricing = PlanConfigService.getCreditPricing('pro');

      return reply.send({
        success: true,
        plans: {
          free: freePlan,
          pro: proPlan
        },
        creditPricing: {
          free: freeCreditPricing,
          pro: proCreditPricing
        }
      });
    } catch (error: any) {
      fastify.log.error({ error: error.message }, 'Failed to get plan configurations');
      return reply.code(500).send({
        statusCode: 500,
        error: 'Internal Server Error',
        message: 'Failed to retrieve plan configurations'
      });
    }
  });
};
