# Email Attachment Processing Implementation Plan

## Overview
This document outlines the phased implementation of email attachment processing capabilities, including storage options, user management, and enhanced CRUD operations.

## Phase 3: Email Attachment Processing Foundation

### 3.1 Enhanced Attachment Processing (Immediate)
**Current State**: Attachments are parsed but content is always included as base64
**Goal**: Implement size-based processing with configurable limits

#### Implementation Steps:
1. **Add attachment size limits to configuration**
   - Small attachments (< 1MB): Include as base64 in webhook payload
   - Large attachments (> 1MB): Store externally and provide URL

2. **Update email parser with size-based logic**
   - Check attachment size before processing
   - Apply different handling based on size and user settings

3. **Add attachment metadata tracking**
   - Store attachment info in database for audit/billing
   - Track storage usage per user

### 3.2 S3-Compatible Storage Infrastructure
**Goal**: Implement flexible storage backend for large attachments

#### Storage Options:
1. **Default (Our S3)**: Managed storage for all users
2. **User-Configured S3**: Allow users to configure their own buckets
3. **Temporary Storage**: 1-hour retention for free users, configurable for paid

#### Implementation Components:
- Storage service abstraction layer
- S3-compatible client configuration
- Temporary file cleanup jobs
- Storage usage tracking and billing

### 3.3 User Storage Configuration Management
**Goal**: Allow users to configure their storage preferences

#### New Settings Section:
- Storage tab in SettingsPage.vue
- S3 bucket configuration form
- Storage usage dashboard
- Retention policy settings

## Phase 4: Enhanced User Management (Edit Capabilities)

### 4.1 Domain Management Enhancement
**Current**: Read-only domain listing
**Goal**: Full CRUD operations with storage integration

#### New Features:
- Edit domain settings
- Configure default storage per domain
- Domain-level attachment policies
- DNS record management UI

### 4.2 Alias Management Enhancement
**Current**: Basic alias creation
**Goal**: Advanced alias configuration

#### New Features:
- Edit existing aliases
- Alias-specific storage settings
- Forwarding rules configuration
- Alias-level attachment handling

### 4.3 Webhook Management Enhancement
**Current**: Basic webhook creation
**Goal**: Advanced webhook configuration

#### New Features:
- Edit webhook endpoints
- Webhook-specific attachment settings
- Retry policy configuration
- Webhook testing tools

## Technical Dependencies

### Database Schema Changes
```sql
-- Storage configurations
CREATE TABLE storage_configs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES users(id),
  name VARCHAR(255) NOT NULL,
  type VARCHAR(50) NOT NULL, -- 'default', 's3', 'user_s3'
  config JSONB NOT NULL,
  is_default BOOLEAN DEFAULT false,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- Attachment storage tracking
CREATE TABLE attachment_storage (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  email_log_id UUID NOT NULL REFERENCES email_logs(id),
  filename VARCHAR(255),
  content_type VARCHAR(255),
  size_bytes BIGINT NOT NULL,
  storage_config_id UUID REFERENCES storage_configs(id),
  storage_path VARCHAR(500),
  expires_at TIMESTAMP,
  created_at TIMESTAMP DEFAULT NOW()
);
```

### Environment Variables
```bash
# Default S3 Configuration
DEFAULT_S3_BUCKET=eu-email-attachments
DEFAULT_S3_REGION=eu-west-1
DEFAULT_S3_ACCESS_KEY=...
DEFAULT_S3_SECRET_KEY=...

# Attachment Processing
MAX_INLINE_ATTACHMENT_SIZE_MB=1
DEFAULT_ATTACHMENT_RETENTION_HOURS=1
PAID_ATTACHMENT_RETENTION_HOURS=24
```

## Implementation Priority

### Immediate (Phase 3.1)
1. ✅ Fix WebSocket configuration for production
2. ✅ Optimize TabNavigation for mobile
3. 🔄 Implement size-based attachment processing
4. 🔄 Add attachment configuration to env

### Short-term (Phase 3.2-3.3)
1. Storage service abstraction
2. S3 integration
3. Storage settings UI
4. Usage tracking

### Medium-term (Phase 4)
1. Enhanced domain management
2. Advanced alias configuration
3. Webhook management improvements
4. Full CRUD operations

## Testing Strategy
- Unit tests for storage services
- Integration tests for attachment processing
- E2E tests for user storage configuration
- Load testing for large attachment handling

## Security Considerations
- Secure S3 credential storage
- Attachment content scanning
- Access control for stored files
- Audit logging for storage operations
