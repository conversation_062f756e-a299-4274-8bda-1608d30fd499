# Test Webhook Feature

The test webhook feature allows users to instantly try the email-to-webhook functionality without setting up their own domain or webhook endpoint.

## How It Works

1. **User Signs Up** → Gets a unique test email address
2. **Send Email** → User sends email to their test address
3. **Real-time Processing** → Email is processed and stored instantly
4. **View Results** → User sees the webhook payload in their logs

## Test Email Format

Each user gets a unique test email based on their user ID:
```
[last-8-chars-of-user-id]@test.emailconnect.eu
```

Example: If user ID is `clm123456789abcdef`, test email is `<EMAIL>`

## Technical Implementation

### Backend Components

1. **Email Processing Route** (`src/backend/routes/email.ts`)
   - Detects emails to `test.emailconnect.eu` domain
   - Extracts user ID suffix from email address
   - Processes email without external webhook delivery

2. **User ID Lookup** 
   - Finds user by last 8 characters of their ID
   - Uses database index for performance
   - Handles edge cases gracefully

3. **WebSocket Integration**
   - Real-time notifications when emails are processed
   - Automatic UI updates in logs and metrics

### Frontend Components

1. **Test Email Card** (`src/frontend/components/onboarding/TestEmailCard.vue`)
   - Shows user their test email address
   - Copy-to-clipboard functionality
   - Links to logs page

2. **Enhanced Logs View**
   - Special badges for test webhooks
   - Webhook payload viewer modal
   - Real-time updates via WebSocket

## Database Schema

```sql
-- Added to emails table
webhook_payload   JSONB           -- Stores full webhook payload
is_test_webhook   BOOLEAN         -- Flags test webhook emails
domain_id         STRING?         -- Optional for test webhooks
```

## Configuration

### DNS Setup
```
test.emailconnect.eu.    IN    MX    10    emailconnect.eu.
```

### Environment Variables
No additional environment variables required - uses existing email processing infrastructure.

## Testing

### Automated Tests
```bash
# Run all webhook tests
npm run test:webhook

# Run manual test script
npm run test:webhook:manual
```

### Manual Testing
1. Sign up for an account
2. Note your test email address in the dashboard
3. Send an email to that address from any email client
4. Check the logs tab for real-time results

## Usage Tracking

Test webhook emails count toward user's monthly email limit, same as regular emails. This ensures:
- Fair usage across all features
- Consistent billing model
- Prevents abuse of the test feature

## Security Considerations

1. **User ID Collision**: 8-character suffixes provide ~596 billion combinations
2. **Rate Limiting**: Standard email processing limits apply
3. **Data Retention**: Test emails expire after 30 days (configurable)
4. **Authentication**: WebSocket connections require valid JWT tokens

## Performance

- **User Lookup**: Indexed on user ID suffix for O(1) lookups
- **Real-time Updates**: WebSocket events only sent to connected users
- **Storage**: Webhook payloads stored as JSONB for efficient querying

## Monitoring

Key metrics to monitor:
- Test webhook email volume
- User ID collision rate (should be 0)
- WebSocket connection count
- Test-to-production conversion rate

## Troubleshooting

### Common Issues

1. **Email not appearing in logs**
   - Check MX record configuration
   - Verify user ID suffix extraction
   - Check email processing logs

2. **WebSocket not connecting**
   - Verify JWT token validity
   - Check browser console for errors
   - Confirm WebSocket server initialization

3. **Payload not showing**
   - Verify `webhookPayload` field in database
   - Check JSON parsing in frontend
   - Confirm modal event handlers

### Debug Commands

```bash
# Check user ID suffix for email
node -e "console.log('<EMAIL>'.split('@')[0])"

# Find user by suffix
npx prisma studio
# Query: User.findFirst({ where: { id: { endsWith: 'abcdefgh' } } })

# Check test webhook emails
npx prisma studio
# Query: Email.findMany({ where: { isTestWebhook: true } })
```

## Future Enhancements

1. **Custom Test Domains**: Allow users to choose from multiple test domains
2. **Webhook Simulation**: Show what the HTTP request would look like
3. **Email Templates**: Pre-built test emails for different scenarios
4. **Analytics Dashboard**: Track test webhook usage and conversion
5. **A/B Testing**: Different onboarding flows for test webhooks

## Migration Path

Users can easily migrate from test webhooks to production:
1. Set up their own domain
2. Configure MX records
3. Create webhooks
4. Start receiving emails on their domain

The test webhook serves as a perfect proof-of-concept that demonstrates value before requiring technical setup.
