# Miscellaneous Documentation

## Overview

This document contains additional operational information, migration notes, API guides, and other miscellaneous documentation that supports the EU Email Webhook service.

## Vue3 Migration Summary

### Migration Completed ✅
Successfully completed the Vue3 migration by removing Alpine.js dependencies and implementing Vue3 components with DaisyUI styling.

**Key Achievements:**
- ✅ Converted all Alpine.js modals to Vue3 components
- ✅ Converted toasts and confirmation dialogs to Vue3
- ✅ Converted tables with sorting to Vue3 components
- ✅ Reduced CSS from 706 lines to 67 lines (90% reduction)
- ✅ Removed 4 redundant JavaScript files
- ✅ Added memory leak prevention with proper cleanup
- ✅ 100% DaisyUI component usage for consistency

**New Vue3 Components Created:**
- `Toast.vue` and `ToastContainer.vue` - Toast notifications
- `ConfirmDialog.vue` - Confirmation dialogs
- `DataTable.vue` - Generic sortable tables
- `DomainsTable.vue`, `WebhooksTable.vue`, `AliasesTable.vue` - Specific tables

**Performance Improvements:**
- 90% reduction in CSS file size
- Memory leak prevention with proper event cleanup
- Optimized component re-rendering with <PERSON><PERSON>'s reactivity

## Alias API Guide

### Overview
Aliases allow you to create specific email addresses within verified domains that route to different webhooks.

### Key Endpoints

**List User Aliases:**
```bash
GET /api/aliases
```

**Create New Alias:**
```bash
POST /api/aliases
{
  "email": "<EMAIL>",
  "domainId": "domain_456",
  "webhookId": "webhook_789",
  "active": true
}
```

**Update Alias:**
```bash
PUT /api/aliases/{aliasId}
{
  "webhookId": "webhook_new",
  "active": false
}
```

**Delete Alias:**
```bash
DELETE /api/aliases/{aliasId}
```

### Reserved Local Parts
Cannot be used for aliases: admin, administrator, root, postmaster, webmaster, hostmaster, abuse, security, noreply, no-reply, mailer-daemon, daemon, system, api, www

### Best Practices
1. Verify domains first - aliases only work for verified domains
2. Use descriptive webhook names
3. Test webhook endpoints before creating aliases
4. Monitor webhook delivery statistics
5. Use HMAC secrets for security

## Domain Setup Guide (Quick Reference)

### DNS Configuration Examples

**Cloudflare:**
```
Type: MX, Name: @, Content: emailconnect.eu, Priority: 10
Type: TXT, Name: @, Content: verify-ec=yourdomain.com
```

**Google Domains:**
```
MX Record: @ → emailconnect.eu (Priority: 10)
TXT Record: @ → verify-ec=yourdomain.com
```

### Integration Examples

**Node.js Express:**
```javascript
app.post('/webhook', (req, res) => {
  const email = req.body;
  console.log('Received email:', email.subject);
  // Process email
  res.status(200).send('OK');
});
```

**Python Flask:**
```python
@app.route('/webhook', methods=['POST'])
def handle_email():
    email = request.json
    print(f"Received email: {email['subject']}")
    # Process email
    return 'OK', 200
```

**PHP:**
```php
<?php
$email = json_decode(file_get_contents('php://input'), true);
error_log("Received email: " . $email['subject']);
// Process email
http_response_code(200);
echo 'OK';
?>
```

## Development Notes

### Current Task List Status
See `TASK_LIST.md` for the current development roadmap including:
- Phase 4: Dashboard Interface Implementation (Vue 3 + Tailwind CSS)
- Phase 5: Frontend Interactivity 
- Testing Implementation with Jest
- Production Readiness improvements

### Technology Stack Notes
- **Backend**: Node.js + Fastify + TypeScript + Prisma
- **Frontend**: Vue 3 + Composition API + TypeScript + Tailwind CSS + DaisyUI
- **Database**: PostgreSQL (app data) + SQLite (Postfix config)
- **Infrastructure**: Docker Compose V2 + GitHub Actions CI/CD
- **Hosting**: Hetzner (Germany) for GDPR compliance

### GDPR Compliance Features
- Automatic email data expiration (30 days)
- EU-only server deployment (Hetzner Germany)
- Audit logging for all operations
- Data export functionality
- Right to erasure (domain deletion removes all data)

## Useful Commands Reference

### Docker Operations
```bash
# Always use --env-file .env.prod for consistency
docker compose -f docker-compose.prod.yml --env-file .env.prod up -d
docker compose -f docker-compose.prod.yml --env-file .env.prod logs -f
docker compose -f docker-compose.prod.yml --env-file .env.prod ps
```

### Database Operations
```bash
# PostgreSQL
docker compose exec postgres psql -U postgres -d eu_email_webhook

# SQLite (Postfix configuration)
sqlite3 /opt/eu-email-webhook/data/postfix.db ".tables"
sqlite3 /opt/eu-email-webhook/data/postfix.db "SELECT * FROM virtual_domains;"
```

### Health Checks
```bash
# Application health
curl https://emailconnect.eu/health
curl http://localhost:3000/health
curl http://localhost:3001/health

# System health
sudo systemctl status nginx
sudo systemctl status postfix
```

## Links and References

### Production Environment
- **Primary Service**: https://emailconnect.eu
- **Server**: ************** (Hetzner, Germany)
- **App Directory**: /home/<USER>/eu-email-webhook

### Documentation Structure
- `BUSINESS_CONTEXT.md` - Project overview and business requirements
- `SERVER_PROVISIONING.md` - One-time server setup procedures
- `DEPLOYMENT.md` - Application deployment and CI/CD
- `TESTING.md` - Testing procedures and strategies
- `OPERATIONS.md` - Troubleshooting and maintenance
- `TASK_LIST.md` - Current development roadmap

---

**Note**: This service is production-deployed and operational. Current development focus is on customer-facing features and operational improvements rather than infrastructure changes.
