# n8n Integration Implementation Plan

## Overview

This document outlines the implementation plan to accommodate the n8n-nodes-emailconnect project requirements. Based on the analysis in `/home/<USER>/webapps/n8n-nodes-emailconnect/docs/TODO.md`, we need to make several backend changes to provide a better API experience for the n8n node.

## Key Issues to Address

1. **Catch-All Alias Validation**: Backend rejects `*@domain.com` due to strict email validation
2. **OpenAPI Schema Mismatches**: Documented schemas don't match actual API behavior
3. **Missing Specialized Endpoint**: Need atomic webhook+alias creation endpoint
4. **Field Naming Inconsistencies**: Different field names across endpoints
5. **Webhook Synchronization**: Need bidirectional sync between domains and catch-all aliases

## Implementation Strategy

**Backend-Focused Approach**: Create new specialized endpoints and fix existing schemas rather than trying to patch multiple frontend API calls. This provides:
- Simplified n8n node logic
- Better error handling and validation
- Centralized webhook synchronization logic
- Reduced API call complexity

## Task Breakdown

### Phase 1: Fix Catch-All Alias Validation ✅ COMPLETED
**Files modified:**
- `src/backend/schemas/user/alias.schemas.ts` ✅
- `src/backend/services/user/alias.service.ts` ✅

**Changes completed:**
1. ✅ Removed `format: 'email'` constraint from CreateAliasRequest schema
2. ✅ Updated validation function to support `*@domain.com` format
3. ✅ Updated error handling for catch-all aliases
4. ✅ Added comprehensive unit tests

### Phase 2: Fix API Schema Inconsistencies ✅ COMPLETED
**Files modified:**
- `src/backend/schemas/openapi-schemas.ts` ✅
- `src/backend/openapi-spec.ts` ✅

**Changes completed:**
1. ✅ Fixed misleading `destinationEmail` field name → now uses `email`, `webhookId`, `domainId`
2. ✅ Removed hallucinated fields: `dkimPublicKey`, `dkimSelector`, `sender`, `recipient`
3. ✅ Updated domain schema to match actual service response format
4. ✅ Added proper webhook and email log schemas based on real implementation
5. ✅ Fixed field naming inconsistencies and added missing enum values
6. ✅ Ensured API documentation at `/docs` shows accurate examples

### Phase 3: Create New POST /api/webhooks/alias Endpoint ✅ COMPLETED
**Files created:**
- `src/backend/schemas/user/webhook-alias.schemas.ts` ✅
- `src/backend/routes/user-webhook-alias.routes.ts` ✅
- `src/backend/controllers/user/webhook-alias.controller.ts` ✅
- `src/backend/services/user/webhook-alias.service.ts` ✅

**Endpoint implemented:**
```typescript
POST /api/webhooks/alias ✅ WORKING
{
  domainId: string;
  webhookUrl: string;
  webhookName: string;
  webhookDescription?: string;
  aliasType: 'catchall' | 'specific';
  localPart?: string; // Required for 'specific'
  syncWithDomain?: boolean; // For catch-all aliases
  autoVerify?: boolean; // Auto-verify using last 5 chars
}
```

### Phase 4: Implement Webhook Synchronization Logic ✅ COMPLETED
**Files created/modified:**
- `src/backend/services/user/webhook-alias.service.ts` ✅
- `src/backend/services/user/webhook-sync.service.ts` ✅
- `prisma/schema.prisma` ✅ (WebhookSync table added)

**Features implemented:**
1. ✅ Bidirectional webhook sync between domains and catch-all aliases
2. ✅ Automatic domain webhook updates when catch-all alias webhook changes
3. ✅ Cleanup logic for webhook sync relationships
4. ✅ Database migration for WebhookSync table

### Phase 5: Update Tests and Documentation ✅ COMPLETED
**Files created/modified:**
- `tests/unit/services/catch-all-alias-validation.test.ts` ✅
- `tests/integration/webhook-alias/` ✅
- `tests/integration/webhook-sync/` ✅
- OpenAPI documentation ✅ Updated

## Detailed Implementation

### 1. Catch-All Alias Validation Fix

**Problem**: Current validation rejects `*@domain.com` due to `format: 'email'` constraint.

**Solution**: 
```typescript
// In alias.schemas.ts - Remove format constraint
email: { 
  type: 'string',
  description: 'Full email address or catch-all pattern (e.g., <EMAIL> or *@domain.com)' 
}

// In alias.service.ts - Custom validation
function isValidEmailOrCatchAll(email: string): boolean {
  // Allow catch-all format
  if (email.match(/^\*@[^\s@]+\.[^\s@]+$/)) {
    return true;
  }
  // Standard email validation
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}
```

### 2. New Specialized Endpoint

**Benefits:**
- Atomic webhook + alias creation
- Custom validation for catch-all aliases
- Built-in domain synchronization
- Auto-verification support
- Single API call for n8n node

**Response Format:**
```typescript
{
  success: boolean;
  webhook: {
    id: string;
    url: string;
    verified: boolean;
    verificationToken?: string;
  };
  alias: {
    id: string;
    email: string;
    active: boolean;
  };
  domain?: {
    id: string;
    webhookUpdated: boolean;
  };
  message: string;
}
```

## Testing Strategy

1. **Unit Tests**: Service layer validation and business logic
2. **Integration Tests**: Full API endpoint testing
3. **E2E Tests**: Complete n8n node workflow simulation
4. **Backward Compatibility**: Ensure existing endpoints still work

## Rollout Plan

1. **Phase 1-2**: Fix validation and schema issues (low risk)
2. **Phase 3**: Add new endpoint (additive, no breaking changes)
3. **Phase 4**: Add synchronization logic (optional feature)
4. **Phase 5**: Comprehensive testing and documentation

## Success Criteria ✅ ALL COMPLETED

- [x] n8n node can create catch-all aliases (`*@domain.com`) ✅
- [x] Single API call creates webhook + alias atomically ✅
- [x] Domain and catch-all alias webhooks stay synchronized ✅
- [x] OpenAPI schema matches actual API behavior ✅
- [x] All existing functionality remains intact ✅
- [x] Comprehensive test coverage for new features ✅

## 🎉 IMPLEMENTATION STATUS: COMPLETE

All phases have been successfully implemented and tested. The n8n-nodes-emailconnect project requirements are now fully supported by the mailwebhook backend API.

### Key Achievements:
1. **Fixed Schema Mismatches**: OpenAPI documentation now accurately reflects actual API behavior
2. **Catch-All Support**: Full support for `*@domain.com` aliases with proper validation
3. **Atomic Operations**: Single endpoint for webhook + alias creation
4. **Webhook Synchronization**: Bidirectional sync between domains and catch-all aliases
5. **Comprehensive Testing**: Unit and integration tests covering all new functionality

### Ready for N8N Integration:
- ✅ All API endpoints documented correctly
- ✅ Request/response formats match documentation
- ✅ Catch-all alias creation working
- ✅ Webhook synchronization operational
- ✅ Auto-verification support implemented

## Next Steps

1. Start with Phase 1 (catch-all validation fix)
2. Test changes with existing functionality
3. Proceed to Phase 2 (schema fixes)
4. Implement Phase 3 (new endpoint)
5. Add Phase 4 (synchronization)
6. Complete Phase 5 (testing and docs)
