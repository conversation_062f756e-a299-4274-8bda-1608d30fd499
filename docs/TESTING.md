 9/9 passing
- **Total Coverage**: 36/36 tests passing (100%)

## Test Structure

### Current Test Scripts (Manual Execution)
```
scripts/
├── test-user-domain-relationships.js    # Phase 1: Domain isolation
├── test-webhook-processing.js           # Phase 1: Webhook routing  
├── test-user-auth.js                    # Phase 2: Authentication
├── test-admin-auth.js                   # Phase 2: Admin system
├── test-user-isolation.js               # Phase 3: Multi-tenancy
└── test-frontend-migration.js           # Frontend: Vue3 validation
```

### Proposed Jest Test Structure
```
tests/
├── integration/
│   ├── auth/
│   │   ├── user-auth.test.js         # Phase 2 tests
│   │   └── admin-auth.test.js
│   ├── domains/
│   │   ├── domain-crud.test.js       # Phase 1 + 3 tests  
│   │   ├── domain-verification.test.js
│   │   └── user-isolation.test.js    # Phase 3 isolation
│   ├── webhooks/
│   │   ├── webhook-crud.test.js
│   │   └── webhook-delivery.test.js
│   └── dashboard/
│       └── metrics.test.js
├── unit/
│   ├── services/
│   │   ├── email-parser.test.js
│   │   ├── dns-verifier.test.js
│   │   └── queue.test.js
│   └── middleware/
│       └── auth.test.js
└── e2e/
    ├── user-journey.test.js          # Complete user flows
    └── admin-journey.test.js
```

## Running Tests

### Manual Test Scripts (Current)

**Phase 1: Email Processing Tests**
```bash
# Test domain-user relationships
node scripts/test-user-domain-relationships.js

# Test webhook processing and routing
node scripts/test-webhook-processing.js
```

**Phase 2: Authentication Tests**
```bash
# Test user authentication system
node scripts/test-user-auth.js

# Test admin authentication system  
node scripts/test-admin-auth.js
```

**Phase 3: User Isolation Tests**
```bash
# Test multi-tenant user isolation
node scripts/test-user-isolation.js
```

**Frontend Tests**
```bash
# Test Vue3 migration validation
node scripts/test-frontend-migration.js
```

### Development Environment Tests
```bash
# Start test environment
npm run docker:dev

# Run all manual tests
npm run test:all

# Run specific test phase
npm run test:phase1
npm run test:phase2
npm run test:phase3
```

## Jest Migration (Planned)

### Setup Jest Infrastructure
```bash
# Install Jest dependencies
npm install --save-dev jest @jest/globals supertest

# Create Jest configuration
# See jest.config.js for TypeScript + ESM support
```

### Jest Configuration
```javascript
export default {
  preset: 'ts-jest/presets/default-esm',
  extensionsToTreatAsEsm: ['.ts'],
  testEnvironment: 'node',
  setupFilesAfterEnv: ['<rootDir>/tests/setup.js'],
  testMatch: ['<rootDir>/tests/**/*.test.js'],
  collectCoverageFrom: [
    'src/**/*.{js,ts}',
    '!src/index.ts',
    '!src/**/*.d.ts'
  ],
  coverageThreshold: {
    global: {
      branches: 80,
      functions: 80,
      lines: 80,
      statements: 80
    }
  }
};
```

### Test Database Setup
```javascript
// tests/setup.js
import { prisma } from '../src/lib/prisma.js';

beforeAll(async () => {
  process.env.DATABASE_URL = process.env.TEST_DATABASE_URL;
  await prisma.$connect();
});

afterAll(async () => {
  await prisma.$disconnect();
});

beforeEach(async () => {
  // Clean database before each test
  await prisma.auditLog.deleteMany();
  await prisma.email.deleteMany();
  await prisma.alias.deleteMany();
  await prisma.domain.deleteMany();
  await prisma.webhook.deleteMany();
  await prisma.user.deleteMany();
});
```

## Integration Tests

### User Isolation Testing
```javascript
// Example: tests/integration/domains/user-isolation.test.js
describe('Domain User Isolation', () => {
  test('users can only see their own domains', async () => {
    // Create domains for different users
    // Verify complete isolation
    // Test API access controls
  });
});
```

### Email Processing Testing
```javascript
// Example: tests/integration/webhooks/webhook-delivery.test.js
describe('Webhook Delivery', () => {
  test('emails route to correct webhooks', async () => {
    // Setup domain with specific webhook
    // Send test email
    // Verify webhook delivery
  });
});
```

## Production Validation

### Deployment Validation Script
```bash
# Run comprehensive production validation
cd /opt/eu-email-webhook/deploy
./validate-deployment.sh

# Expected output:
# ✅ Container Health Check
# ✅ Service Connectivity
# ✅ Database Connectivity  
# ✅ External API Access
# ✅ Email Processing Ready
# ✅ Webhook Delivery Functional
```

### Manual Production Tests
```bash
# Health checks
curl https://emailconnect.eu/health
curl http://localhost:3000/health
curl http://localhost:3001/health

# API functionality
curl -X POST https://emailconnect.eu/api/domains \
  -H "Content-Type: application/json" \
  -d '{"domain": "test.example.com", "webhookUrl": "https://webhook.site/xxx"}'

# Email processing test
echo "Test email" | mail -s "Test" <EMAIL>
```

## Testing Best Practices

### Test Data Management
- Use isolated test database
- Clean state between tests
- Use factories for test data creation
- Mock external dependencies (DNS, webhooks)

### Test Coverage Goals
- **Unit Tests**: 90%+ coverage for core business logic
- **Integration Tests**: All API endpoints and workflows
- **E2E Tests**: Critical user journeys
- **Performance Tests**: Load testing for email processing

### CI/CD Integration
```yaml
# In GitHub Actions workflow
- name: Run Tests
  run: |
    npm run test:unit
    npm run test:integration
    npm run test:e2e

- name: Coverage Report
  run: npm run test:coverage
```

## Test Categories

### 1. Email Processing Tests
- Email parsing and validation
- Webhook payload generation
- Queue processing and retries
- GDPR compliance (data retention)

### 2. Domain Management Tests  
- Domain registration and verification
- DNS TXT record validation
- MX record configuration
- Domain ownership verification

### 3. User Management Tests
- User registration and authentication
- Password reset flows
- Session management
- Multi-tenant isolation

### 4. API Security Tests
- Authentication middleware
- Authorization checks
- Rate limiting
- Input validation and sanitization

### 5. Database Tests
- Data integrity and constraints
- Migration testing
- Backup and restore procedures
- Performance under load

## Testing Tools and Utilities

### Test Helpers
```javascript
// tests/helpers/auth.js
export const createTestUser = async (email) => {
  return prisma.user.create({
    data: { email, hashedPassword: await bcrypt.hash('password', 10) }
  });
};

export const createUserToken = (user) => {
  return jwt.sign({ userId: user.id }, process.env.JWT_SECRET);
};
```

### Mock Services
```javascript
// tests/mocks/webhook.js
export const mockWebhookServer = (port = 3999) => {
  const app = express();
  app.post('/webhook', (req, res) => {
    mockWebhookServer.lastPayload = req.body;
    res.status(200).send('OK');
  });
  return app.listen(port);
};
```

## Load Testing

### Email Processing Load Test
```bash
# Simulate high email volume
for i in {1..100}; do
  echo "Load test email $i" | mail -s "Load Test $i" <EMAIL> &
done
wait
```

### API Load Test
```bash
# Use Apache Bench for API load testing
ab -n 1000 -c 10 https://emailconnect.eu/health
ab -n 100 -c 5 -p domain.json -T application/json https://emailconnect.eu/api/domains
```

## Error Testing

### Network Failure Simulation
- Webhook endpoint downtime
- Database connection failures
- Redis queue unavailability
- DNS resolution failures

### Edge Cases
- Malformed email content
- Extremely large attachments
- Invalid webhook URLs
- Concurrent domain operations

## Performance Benchmarks

### Target Metrics
- **Email Processing**: <30 seconds from receipt to webhook delivery
- **API Response**: <500ms for most endpoints
- **Database Queries**: <100ms average
- **Queue Processing**: 99%+ success rate

### Monitoring in Tests
```javascript
const startTime = Date.now();
await processEmail(emailData);
const processingTime = Date.now() - startTime;
expect(processingTime).toBeLessThan(30000); // 30 seconds
```

## Troubleshooting Test Issues

### Common Test Failures
1. **Database Connection**: Ensure test database is running
2. **Port Conflicts**: Use different ports for test services
3. **Async Issues**: Proper await/promise handling
4. **Race Conditions**: Use proper test isolation

### Debug Mode
```bash
# Run tests with debug output
DEBUG=* npm test

# Run specific test file
npm test -- --grep "specific test description"
```

## Future Testing Enhancements

### Planned Improvements
1. **Jest Migration**: Convert all manual scripts to Jest
2. **E2E Testing**: Playwright or Cypress for full user workflows
3. **Visual Regression**: Screenshot testing for UI components
4. **Performance Monitoring**: Continuous performance testing
5. **Security Testing**: Automated security scans

### Test Automation Goals
- Run tests on every commit
- Prevent broken deployments
- Generate coverage reports
- Performance regression detection
- Security vulnerability scanning

---

**Next Steps**: Migrate from manual test scripts to Jest-based test suite for better CI/CD integration and coverage reporting.
