# EmailConnect Webhook Lifecycle Fixes

This document describes the critical fixes implemented to resolve webhook lifecycle management issues in the EmailConnect n8n trigger node.

## Issues Resolved

### 1. Catch-all Alias Creation Conflicts (409 Errors)

**Problem**: When trying to create a catch-all alias (`*@domain.com`) that already existed, the system would fail with a 409 conflict error instead of updating the existing alias.

**Root Cause**: The backend service in `src/backend/services/user/webhook-alias.service.ts` (lines 92-94) threw an error for any existing alias, including catch-all aliases.

**Fix**: Modified the `createWebhookAlias` method to handle existing catch-all aliases by updating them instead of failing:

```typescript
// For catch-all aliases, update existing instead of failing
if (existingAlias && data.aliasType === 'catchall') {
  console.log(`Updating existing catch-all alias ${emailAddress} with new webhook`);
  
  // Create new webhook and update existing alias
  // Includes domain synchronization for catch-all
}
```

**Result**: ✅ Catch-all aliases can now be updated seamlessly without 409 errors.

### 2. Webhook-<PERSON>as Unlinking During URL Updates

**Problem**: When switching between test and production webhook URLs, the webhook URL would update correctly but the webhook would become "orphaned" - no longer linked to its alias.

**Root Cause**: The `checkExists` method in the n8n trigger node updated webhook URLs but didn't verify that the webhook remained linked to its alias.

**Fix**: Added `ensureWebhookAliasLinkage` function that runs after webhook URL updates to verify and restore alias-webhook relationships:

```typescript
// Ensure webhook-alias linkage is maintained after URL update
await ensureWebhookAliasLinkage(this, webhookId);
```

**Result**: ✅ Webhooks stay properly linked to aliases during test/production URL switching.

### 3. Domain-Catchall Synchronization

**Problem**: Domain and catch-all alias webhooks should stay synchronized, but updates weren't properly maintaining this relationship.

**Fix**: Enhanced the catch-all update logic to include domain synchronization:

```typescript
// Handle domain synchronization for catch-all
if (data.syncWithDomain !== false) {
  // Update domain to use the same webhook
  await tx.domain.update({
    where: { id: data.domainId },
    data: { webhookId: webhookResult.webhook.id }
  });
  
  // Update or create webhook sync relationship
  await tx.webhookSync.upsert({...});
}
```

**Result**: ✅ Domain and catch-all webhooks stay synchronized during updates.

## Implementation Details

### Files Modified

1. **`src/backend/services/user/webhook-alias.service.ts`**
   - Added catch-all alias update logic (lines 92-94 region)
   - Enhanced domain synchronization for catch-all aliases

2. **`nodes/EmailConnectTrigger/EmailConnectTrigger.node.ts`**
   - Added `ensureWebhookAliasLinkage` helper function
   - Enhanced `checkExists` method to maintain webhook-alias linkage
   - Added linkage verification after webhook URL updates

### Key Functions Added

- **`ensureWebhookAliasLinkage(context, webhookId)`**: Verifies and restores webhook-alias relationships after URL updates
- **Enhanced catch-all update logic**: Handles existing catch-all aliases by updating instead of failing

## Testing Results

All fixes have been comprehensively tested:

✅ **Catch-all Creation/Update**: No more 409 errors when updating existing catch-all aliases
✅ **Webhook-Alias Linking**: Webhooks remain linked to aliases during URL updates  
✅ **Domain-Catchall Sync**: Domain and catch-all webhooks stay synchronized
✅ **Webhook Protection**: Webhooks in use are protected from accidental deletion

## Webhook Cleanup on Node Deletion

**Status**: ⚠️ **Known Issue - n8n Framework Limitation**

The `delete()` method in the trigger node contains comprehensive cleanup logic:
- Detaches webhooks from aliases and domains
- Deletes the webhook after detachment
- Handles errors gracefully

**However**: As of n8n 1.15.0, **n8n no longer automatically deregisters webhooks at startup and shutdown**. This means the `delete()` method is **not being called** when nodes are deleted, which explains why webhooks become orphaned.

**Root Cause**: n8n framework change - webhook deregistration was disabled to improve performance, assuming third-party services will retry unhandled requests.

**Current Workaround**: Users can manually delete orphaned webhooks through the EmailConnect UI.

**Future Solution Options**:
1. Implement a cleanup utility/command for orphaned webhooks
2. Add a "cleanup orphaned webhooks" feature to the EmailConnect UI
3. Implement periodic cleanup jobs on the backend
4. Consider alternative cleanup triggers (workflow deactivation, etc.)

## Migration Notes

These fixes are backward compatible and don't require any migration steps. Existing webhooks and aliases will continue to work normally, and the new logic will apply to future operations.

## API Endpoints Affected

- `POST /api/webhooks/alias` - Now handles existing catch-all aliases
- `PUT /api/webhooks/{id}` - Enhanced with linkage preservation
- `PUT /api/aliases/{id}/webhook` - Used for linkage restoration
- `PUT /api/domains/{id}/webhook` - Used for domain synchronization

## Monitoring

The fixes include comprehensive logging to help monitor webhook lifecycle operations:

- Catch-all alias updates are logged with success messages
- Webhook-alias linkage operations are logged with detailed information
- Domain synchronization operations are logged

Look for log messages starting with "EmailConnect:" to track webhook lifecycle operations.
