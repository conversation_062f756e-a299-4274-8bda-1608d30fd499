# Testing Guide - Phase 4 Edit Functionality & Phase 3 Attachments

## 🧪 Testing Phase 4: Edit Functionality

### 1. Domain Editing
**Steps to test:**
1. Navigate to `/domains` tab
2. Click "Edit" button on any domain
3. Verify modal opens with pre-filled data:
   - Domain name (disabled)
   - Current webhook selected
   - Configuration toggles set correctly
4. Change webhook selection
5. Toggle "Allow attachments" and "Include envelope data"
6. Click "Update domain"
7. Verify success message and data refresh

**Expected behavior:**
- ✅ Modal opens with correct data
- ✅ Domain name field is disabled (cannot be changed)
- ✅ Webhook can be changed
- ✅ Configuration toggles work
- ✅ Update saves successfully
- ✅ Table refreshes with new data

### 2. Alias Editing
**Steps to test:**
1. Navigate to `/aliases` tab
2. Click "Edit" button on any alias
3. Verify modal opens with pre-filled data:
   - Email address (disabled)
   - Domain (disabled)
   - Current webhook selected
   - Configuration toggles set correctly
4. Change webhook selection
5. Toggle configuration options
6. Click "Update alias"
7. Verify success message and data refresh

**Expected behavior:**
- ✅ Modal opens with correct data
- ✅ Email and domain fields are disabled
- ✅ Webhook can be changed
- ✅ Configuration toggles work
- ✅ Update saves successfully

### 3. Webhook Editing
**Steps to test:**
1. Navigate to `/webhooks` tab
2. Click "Edit" button on any webhook
3. Verify modal opens with pre-filled data:
   - Name, URL, description filled
4. Change webhook URL
5. Click "Update webhook"
6. Verify re-verification modal opens (if URL changed)
7. Verify success message and data refresh

**Expected behavior:**
- ✅ Modal opens with correct data
- ✅ All fields can be edited
- ✅ URL change triggers re-verification
- ✅ Update saves successfully

## 🧪 Testing Phase 3: Simplified Attachment Processing

### 1. Storage Settings UI
**Steps to test:**
1. Navigate to `/settings`
2. Click "Storage" tab
3. Verify free plan limitations are displayed
4. Verify storage and retention options are disabled
5. Check that explanatory text is clear

**Expected behavior:**
- ✅ Free plan limitations clearly shown
- ✅ 128KB limit and text-only restrictions explained
- ✅ Storage options disabled with explanation
- ✅ UI indicates Pro plan features

### 2. Email Processing with Attachments
**Steps to test:**
1. Send test email with various attachments:
   - Small PDF (< 128KB) ✅ Should be included
   - Large PDF (> 128KB) ❌ Should be excluded (too large)
   - Small image (< 128KB) ❌ Should be excluded (non-text)
   - Small TXT file (< 128KB) ✅ Should be included
   - Small CSV file (< 128KB) ✅ Should be included

2. Check webhook payload for:
   - Included attachments have `content` field with base64 data
   - Excluded attachments have `excluded: true` and `excludeReason`

**Expected webhook payload structure:**
```json
{
  "attachments": [
    {
      "filename": "document.pdf",
      "contentType": "application/pdf",
      "size": 50000,
      "content": "base64-encoded-content..."
    },
    {
      "filename": "large-file.pdf", 
      "contentType": "application/pdf",
      "size": 200000,
      "excluded": true,
      "excludeReason": "too-large"
    },
    {
      "filename": "image.jpg",
      "contentType": "image/jpeg", 
      "size": 30000,
      "excluded": true,
      "excludeReason": "non-text-file"
    }
  ]
}
```

## 🧪 Testing Configuration Toggles

### 1. Domain-Level Configuration
**Steps to test:**
1. Edit a domain and disable "Allow attachments"
2. Send email to that domain with attachments
3. Verify attachments are excluded from webhook payload
4. Re-enable "Allow attachments" and test again

### 2. Alias-Level Configuration  
**Steps to test:**
1. Edit an alias and disable "Include envelope data"
2. Send email to that alias
3. Verify envelope data is excluded from webhook payload
4. Re-enable and test again

## 🧪 Database Migration Testing

### Verify Schema Changes
**Check database:**
```sql
-- Verify configuration column exists
SELECT column_name, data_type 
FROM information_schema.columns 
WHERE table_name IN ('Domain', 'Alias') 
AND column_name = 'configuration';

-- Check sample configuration data
SELECT id, domain, configuration 
FROM "Domain" 
LIMIT 5;

SELECT id, email, configuration 
FROM "Alias" 
LIMIT 5;
```

**Expected results:**
- ✅ `configuration` column exists as JSON type
- ✅ Configuration data is properly stored and retrieved
- ✅ Default values work correctly

## 🧪 API Endpoint Testing

### Test with curl commands:
```bash
# Test domain update
curl -X PUT http://localhost:3000/api/domains/{domain-id} \
  -H "Content-Type: application/json" \
  -H "Cookie: your-session-cookie" \
  -d '{
    "webhookId": "new-webhook-id",
    "allowAttachments": true,
    "includeEnvelope": false
  }'

# Test alias update  
curl -X PUT http://localhost:3000/api/aliases/{alias-id} \
  -H "Content-Type: application/json" \
  -H "Cookie: your-session-cookie" \
  -d '{
    "email": "<EMAIL>",
    "webhookId": "new-webhook-id",
    "allowAttachments": false,
    "includeEnvelope": true
  }'

# Test webhook update
curl -X PUT http://localhost:3000/api/webhooks/{webhook-id} \
  -H "Content-Type: application/json" \
  -H "Cookie: your-session-cookie" \
  -d '{
    "name": "Updated Webhook",
    "url": "https://new-url.com/webhook",
    "description": "Updated description"
  }'
```

## 🚨 Common Issues to Check

### 1. Modal Not Opening
- Check browser console for JavaScript errors
- Verify `window.openModal` function exists
- Check modal template names match exactly

### 2. Form Not Pre-filling
- Verify data structure matches expected format
- Check configuration extraction logic
- Ensure default values are set correctly

### 3. API Errors
- Check authentication cookies
- Verify request payload structure
- Check server logs for validation errors

### 4. Database Issues
- Ensure migration ran successfully
- Check for JSON parsing errors
- Verify foreign key constraints

## ✅ Success Criteria

**Phase 4 is successful when:**
- ✅ All edit buttons work and open modals
- ✅ Forms pre-fill with correct existing data
- ✅ Updates save successfully via API
- ✅ Data refreshes immediately in UI
- ✅ Configuration toggles work as expected

**Phase 3 is successful when:**
- ✅ Small text attachments are included as base64
- ✅ Large/non-text attachments are properly excluded
- ✅ Exclusion reasons are provided
- ✅ Configuration toggles control attachment processing
- ✅ Storage UI clearly shows free plan limitations

## 🎯 Next Steps After Testing

1. **Deploy to production** with WebSocket fix
2. **Monitor attachment processing** in real emails
3. **Gather user feedback** on edit functionality
4. **Plan Pro plan features** for advanced storage options
5. **Consider Phase 5** enhancements based on usage patterns
