#!/bin/bash

# Webhook API Test Script
# This script demonstrates the webhook management functionality

BASE_URL="http://localhost:3000/api"
TOKEN="YOUR_JWT_TOKEN_HERE"  # Replace with actual token

echo "=== Email Connect Webhook API Test ==="
echo ""

# Function to make API requests
api_request() {
    local method=$1
    local endpoint=$2
    local data=${3:-""}
    
    if [ -n "$data" ]; then
        curl -s -X $method "$BASE_URL$endpoint" \
            -H "Authorization: Bearer $TOKEN" \
            -H "Content-Type: application/json" \
            -d "$data" | jq .
    else
        curl -s -X $method "$BASE_URL$endpoint" \
            -H "Authorization: Bearer $TOKEN" | jq .
    fi
}

echo "1. Create a new webhook"
webhook_response=$(api_request "POST" "/webhooks" '{
    "name": "Test Webhook",
    "url": "https://webhook.site/unique-id",
    "description": "Test webhook for API demonstration"
}')
echo "$webhook_response"
webhook_id=$(echo "$webhook_response" | jq -r '.webhook.id')
echo "Created webhook ID: $webhook_id"
echo ""

echo "2. List all webhooks"
api_request "GET" "/webhooks"
echo ""

echo "3. Verify the webhook"
echo "Initiating verification..."
api_request "POST" "/webhooks/$webhook_id/verify"
echo ""

echo "4. Complete verification (you need to get the token from your webhook endpoint)"
echo "# api_request \"POST\" \"/webhooks/$webhook_id/verify/complete\" '{\"verificationToken\": \"token_from_webhook\"}'"
echo ""

echo "5. List domains (to get domain ID for testing)"
domains_response=$(api_request "GET" "/domains")
echo "$domains_response"
domain_id=$(echo "$domains_response" | jq -r '.domains[0].id // empty')
echo ""

if [ -n "$domain_id" ]; then
    echo "6. Update domain webhook"
    api_request "PUT" "/domains/$domain_id/webhook" "{\"webhookId\": \"$webhook_id\"}"
    echo ""
    
    echo "7. List aliases (to get alias ID for testing)"
    aliases_response=$(api_request "GET" "/aliases")
    echo "$aliases_response"
    alias_id=$(echo "$aliases_response" | jq -r '.aliases[0].id // empty')
    echo ""
    
    if [ -n "$alias_id" ]; then
        echo "8. Update alias webhook (NEW FUNCTIONALITY!)"
        api_request "PUT" "/aliases/$alias_id/webhook" "{\"webhookId\": \"$webhook_id\"}"
        echo ""
    else
        echo "No aliases found to test with"
    fi
else
    echo "No domains found to test with"
fi

echo "9. Get specific webhook details"
api_request "GET" "/webhooks/$webhook_id"
echo ""

echo "=== Test Complete ==="
echo ""
echo "Available API endpoints:"
echo "POST   /api/webhooks                     - Create webhook"
echo "GET    /api/webhooks                     - List webhooks"
echo "GET    /api/webhooks/:id                 - Get webhook"
echo "PUT    /api/webhooks/:id                 - Update webhook"
echo "DELETE /api/webhooks/:id                 - Delete webhook"
echo "POST   /api/webhooks/:id/verify          - Verify webhook"
echo "POST   /api/webhooks/:id/verify/complete - Complete verification"
echo "PUT    /api/domains/:id/webhook          - Set domain webhook"
echo "PUT    /api/aliases/:id/webhook          - Set alias webhook (NEW!)"
