#!/bin/bash

# Quick test to verify the alias webhook route is available
# This script checks if the new PUT /api/aliases/:id/webhook endpoint responds

BASE_URL="http://localhost:3000"

echo "=== Testing Alias Webhook Route ==="
echo ""

# Test if the endpoint exists (should return 401 without auth, not 404)
echo "Testing PUT /api/aliases/test-id/webhook endpoint..."
response=$(curl -s -w "HTTP_STATUS:%{http_code}" -X PUT "$BASE_URL/api/aliases/test-id/webhook" \
    -H "Content-Type: application/json" \
    -d '{"webhookId": "test-webhook-id"}')

http_status=$(echo "$response" | grep -o 'HTTP_STATUS:[0-9]*' | cut -d: -f2)
body=$(echo "$response" | sed 's/HTTP_STATUS:[0-9]*$//')

echo "HTTP Status: $http_status"
echo "Response: $body"
echo ""

if [ "$http_status" = "401" ]; then
    echo "✅ SUCCESS: Route is available (returned 401 Unauthorized as expected without token)"
elif [ "$http_status" = "404" ]; then
    echo "❌ FAILED: Route not found (404) - route may not be registered"
else
    echo "ℹ️  Unexpected status: $http_status"
fi

echo ""
echo "Expected behavior:"
echo "- 401: Route exists but requires authentication (GOOD)"
echo "- 404: Route not found (PROBLEM)"
echo ""

# Also test the Swagger docs to see if the route is documented
echo "Checking if route appears in OpenAPI docs..."
docs_response=$(curl -s "$BASE_URL/docs/json" | grep -o 'aliases.*webhook' || echo "Not found in docs")
echo "OpenAPI docs contain alias webhook: $docs_response"
