/**
 * Example TypeScript usage of the Email Connect Webhook API
 * This demonstrates how to programmatically manage webhooks, domains, and aliases
 */

interface WebhookResponse {
  success: boolean;
  webhook?: {
    id: string;
    name: string;
    url: string;
    verified: boolean;
  };
  message?: string;
}

interface DomainWebhookResponse {
  success: boolean;
  domain: {
    id: string;
    domain: string;
    webhookId: string;
    updatedAt: string;
  };
  webhook: {
    id: string;
    name: string;
    url: string;
  };
  message: string;
}

interface AliasWebhookResponse {
  success: boolean;
  alias: {
    id: string;
    email: string;
    webhookId: string;
    updatedAt: string;
  };
  webhook: {
    id: string;
    name: string;
    url: string;
  };
  message: string;
}

class EmailConnectAPI {
  private baseUrl: string;
  private token: string;

  constructor(baseUrl: string = 'https://emailconnect.eu/api', token: string) {
    this.baseUrl = baseUrl;
    this.token = token;
  }

  private async request<T>(
    method: string,
    endpoint: string,
    body?: any
  ): Promise<T> {
    const response = await fetch(`${this.baseUrl}${endpoint}`, {
      method,
      headers: {
        'Authorization': `Bearer ${this.token}`,
        'Content-Type': 'application/json',
      },
      body: body ? JSON.stringify(body) : undefined,
    });

    if (!response.ok) {
      throw new Error(`API request failed: ${response.status} ${response.statusText}`);
    }

    return response.json();
  }

  // ===== Webhook Management =====

  async createWebhook(name: string, url: string, description?: string): Promise<WebhookResponse> {
    return this.request<WebhookResponse>('POST', '/webhooks', {
      name,
      url,
      description,
    });
  }

  async listWebhooks(): Promise<{ webhooks: any[]; total: number }> {
    return this.request('GET', '/webhooks');
  }

  async getWebhook(webhookId: string): Promise<any> {
    return this.request('GET', `/webhooks/${webhookId}`);
  }

  async updateWebhook(webhookId: string, updates: { name?: string; url?: string; description?: string }): Promise<WebhookResponse> {
    return this.request<WebhookResponse>('PUT', `/webhooks/${webhookId}`, updates);
  }

  async deleteWebhook(webhookId: string): Promise<{ success: boolean; message: string }> {
    return this.request('DELETE', `/webhooks/${webhookId}`);
  }

  // ===== Webhook Verification =====

  async verifyWebhook(webhookId: string): Promise<{ success: boolean; verificationToken: string }> {
    return this.request('POST', `/webhooks/${webhookId}/verify`);
  }

  async completeWebhookVerification(webhookId: string, verificationToken: string): Promise<{ success: boolean; verified: boolean }> {
    return this.request('POST', `/webhooks/${webhookId}/verify/complete`, {
      verificationToken,
    });
  }

  // ===== Domain Webhook Assignment =====

  async setDomainWebhook(domainId: string, webhookId: string): Promise<DomainWebhookResponse> {
    return this.request<DomainWebhookResponse>('PUT', `/domains/${domainId}/webhook`, {
      webhookId,
    });
  }

  // ===== Alias Webhook Assignment =====

  async setAliasWebhook(aliasId: string, webhookId: string): Promise<AliasWebhookResponse> {
    return this.request<AliasWebhookResponse>('PUT', `/aliases/${aliasId}/webhook`, {
      webhookId,
    });
  }

  // ===== Domain and Alias Management =====

  async listDomains(): Promise<{ domains: any[]; total: number }> {
    return this.request('GET', '/domains');
  }

  async listAliases(): Promise<{ aliases: any[]; total: number }> {
    return this.request('GET', '/aliases');
  }
}

// ===== Usage Examples =====

async function example() {
  const api = new EmailConnectAPI('https://emailconnect.eu/api', 'your-jwt-token');

  try {
    // 1. Create a webhook
    console.log('Creating webhook...');
    const webhook = await api.createWebhook(
      'Production API',
      'https://your-app.com/webhook',
      'Main production webhook endpoint'
    );
    console.log('Webhook created:', webhook);

    const webhookId = webhook.webhook!.id;

    // 2. Verify the webhook
    console.log('Initiating webhook verification...');
    const verification = await api.verifyWebhook(webhookId);
    console.log('Verification initiated:', verification);

    // Note: In real usage, you would receive the verification token
    // in your webhook endpoint and then complete verification:
    // await api.completeWebhookVerification(webhookId, 'received-token');

    // 3. Get domains and set webhook
    console.log('Getting domains...');
    const domains = await api.listDomains();
    if (domains.domains.length > 0) {
      const domainId = domains.domains[0].id;
      console.log('Setting domain webhook...');
      const domainUpdate = await api.setDomainWebhook(domainId, webhookId);
      console.log('Domain webhook updated:', domainUpdate);
    }

    // 4. Get aliases and set webhook
    console.log('Getting aliases...');
    const aliases = await api.listAliases();
    if (aliases.aliases.length > 0) {
      const aliasId = aliases.aliases[0].id;
      console.log('Setting alias webhook...');
      const aliasUpdate = await api.setAliasWebhook(aliasId, webhookId);
      console.log('Alias webhook updated:', aliasUpdate);
    }

    // 5. List all webhooks
    console.log('Listing all webhooks...');
    const allWebhooks = await api.listWebhooks();
    console.log('All webhooks:', allWebhooks);

  } catch (error) {
    console.error('API error:', error);
  }
}

// ===== Webhook Flow Example =====

/**
 * Complete webhook setup workflow
 */
async function setupWebhookFlow() {
  const api = new EmailConnectAPI('https://emailconnect.eu/api', 'your-jwt-token');

  try {
    // Step 1: Create webhook
    const webhook = await api.createWebhook(
      'My App Webhook',
      'https://my-app.com/webhooks/email'
    );
    const webhookId = webhook.webhook!.id;

    // Step 2: Verify webhook
    await api.verifyWebhook(webhookId);
    
    // Step 3: In your webhook endpoint, extract the verification token and complete verification
    // This would be done in your webhook handler:
    // const { verificationToken } = req.body;
    // await api.completeWebhookVerification(webhookId, verificationToken);

    // Step 4: Assign webhook to domains and aliases
    const domains = await api.listDomains();
    for (const domain of domains.domains) {
      await api.setDomainWebhook(domain.id, webhookId);
      console.log(`Webhook assigned to domain: ${domain.domainName}`);
    }

    const aliases = await api.listAliases();
    for (const alias of aliases.aliases) {
      await api.setAliasWebhook(alias.id, webhookId);
      console.log(`Webhook assigned to alias: ${alias.email}`);
    }

    console.log('Webhook setup complete!');

  } catch (error) {
    console.error('Setup failed:', error);
  }
}

export { EmailConnectAPI, setupWebhookFlow, example };
