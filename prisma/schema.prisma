// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id        String   @id @default(cuid())
  email     String   @unique
  password  String   // bcrypt hashed
  name      String?
  verified  <PERSON><PERSON><PERSON>  @default(false)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  
  // Usage tracking for billing/limits
  monthlyEmailLimit   Int      @default(50)
  currentMonthEmails  Int      @default(0)
  lastUsageReset      DateTime @default(now())
  planType           String   @default("free") // "free", "pro", "enterprise"
  
  // Relations
  domains       Domain[]
  webhooks      Webhook[]
  apiKeys       ApiKey[]
  subscriptions Subscription[]
  payments      Payment[]
  paymentMethods PaymentMethod[]
  creditBatches CreditBatch[]
  creditTransactions CreditTransaction[]

  @@map("users")
}

model Webhook {
  id          String   @id @default(cuid())
  name        String   // e.g., "Production API", "Test Endpoint"
  url         String
  description String?
  active      Boolean  @default(true)
  webhookSecret String? // Optional HMAC secret for webhook signature verification
  verified    Boolean  @default(false) // Whether webhook URL has been verified
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  userId      String
  user        User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  domains     Domain[] // Domains using this as default webhook
  aliases     Alias[]  // Aliases using this specific webhook

  // Webhook synchronization
  webhookSyncs WebhookSync[]

  @@map("webhooks")
}

model Domain {
  id          String   @id @default(cuid())
  domain      String   @unique
  active      Boolean  @default(true)
  verified    Boolean  @default(false)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // DNS verification workflow
  verificationStatus        VerificationStatus @default(PENDING)
  lastVerificationAttempt   DateTime?
  nextVerificationCheck     DateTime?
  verificationFailureCount  Int               @default(0)
  verificationToken         String?           // For alternative verification methods

  // GDPR compliance
  dataRetentionDays Int @default(30)

  // Email processing configuration
  configuration Json? // { allowAttachments: boolean, includeEnvelope: boolean }

  // User ownership (required)
  userId      String
  user        User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  // Webhook relationship (required)
  webhookId   String
  webhook     Webhook  @relation(fields: [webhookId], references: [id], onDelete: Restrict)

  // Relations
  aliases     Alias[]
  emails      Email[]

  // Webhook synchronization
  webhookSyncs WebhookSync[]

  @@map("domains")
}

model Alias {
  id          String   @id @default(cuid())
  email       String   // e.g., "<EMAIL>"
  active      Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Email processing configuration
  configuration Json? // { allowAttachments: boolean, includeEnvelope: boolean }

  // Relations
  domainId    String
  domain      Domain   @relation(fields: [domainId], references: [id], onDelete: Cascade)

  // Webhook relationship (required - each alias has its own webhook)
  webhookId   String
  webhook     Webhook  @relation(fields: [webhookId], references: [id], onDelete: Restrict)

  // Webhook synchronization (for catch-all aliases)
  webhookSyncs WebhookSync[]

  @@unique([email, domainId])
  @@map("aliases")
}

// Webhook synchronization for catch-all aliases and domains
model WebhookSync {
  id              String   @id @default(cuid())
  domainId        String
  catchAllAliasId String
  webhookId       String
  syncEnabled     Boolean  @default(true)
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  // Relations
  domain    Domain  @relation(fields: [domainId], references: [id], onDelete: Cascade)
  alias     Alias   @relation(fields: [catchAllAliasId], references: [id], onDelete: Cascade)
  webhook   Webhook @relation(fields: [webhookId], references: [id], onDelete: Cascade)

  // Ensure only one sync relationship per domain-alias pair
  @@unique([domainId, catchAllAliasId])
  @@map("webhook_syncs")
}

model Email {
  id               String          @id @default(cuid())
  messageId        String          @unique
  fromAddress      String
  toAddresses      String[]
  subject          String?

  // Delivery tracking
  deliveryStatus   DeliveryStatus  @default(PENDING)
  deliveryAttempts Int            @default(0)
  lastAttemptAt    DateTime?
  deliveredAt      DateTime?
  errorMessage     String?

  // Test webhook support
  webhookPayload   Json?           // Store webhook payload for test webhooks
  isTestWebhook    Boolean         @default(false)

  // GDPR compliance - auto-expire emails
  expiresAt        DateTime
  createdAt        DateTime        @default(now())
  updatedAt        DateTime        @updatedAt

  // Relations (domainId is optional for test webhooks)
  domainId         String?
  domain           Domain?         @relation(fields: [domainId], references: [id], onDelete: Cascade)

  @@map("emails")
}

enum DeliveryStatus {
  PENDING
  DELIVERED
  FAILED
  RETRYING
  EXPIRED
}

enum VerificationStatus {
  PENDING     // Just added, waiting for verification
  VERIFIED    // DNS verification successful
  ACTIVE      // Verified and receiving emails
  WARNING     // Verification failed, grace period active
  SUSPENDED   // Grace period expired, domain disabled
  FAILED      // Verification permanently failed
}

// Optional: Audit log for GDPR compliance
model AuditLog {
  id          String   @id @default(cuid())
  action      String   // e.g., "email_processed", "webhook_delivered", "data_deleted"
  resourceId  String?  // ID of the affected resource
  resourceType String? // e.g., "email", "domain"
  metadata    Json?    // Additional context
  ipAddress   String?
  userAgent   String?
  createdAt   DateTime @default(now())
  
  // Auto-expire audit logs for GDPR compliance
  expiresAt   DateTime
  
  @@map("audit_logs")
}

// Payment and subscription models for Mollie integration
model Subscription {
  id                String              @id @default(cuid())
  mollieId          String              @unique // Mollie subscription ID
  status            SubscriptionStatus  @default(PENDING)
  planType          String              // "pro", "enterprise"
  interval          String              // "monthly", "yearly"
  amount            Decimal             @db.Decimal(10, 2)
  currency          String              @default("EUR")
  description       String?

  // Mollie webhook data
  mollieCustomerId  String?             // Mollie customer ID
  molliePaymentMethod String?           // Payment method used

  // Subscription lifecycle
  startDate         DateTime?
  nextPaymentDate   DateTime?
  cancelledAt       DateTime?
  cancelReason      String?

  // Metadata
  createdAt         DateTime            @default(now())
  updatedAt         DateTime            @updatedAt

  // Relations
  userId            String
  user              User                @relation(fields: [userId], references: [id], onDelete: Cascade)
  payments          Payment[]

  @@map("subscriptions")
}

model Payment {
  id                String        @id @default(cuid())
  mollieId          String        @unique // Mollie payment ID
  status            PaymentStatus @default(PENDING)
  amount            Decimal       @db.Decimal(10, 2)
  currency          String        @default("EUR")
  description       String?
  method            String?       // Payment method (ideal, creditcard, etc.)

  // Payment lifecycle
  paidAt            DateTime?
  cancelledAt       DateTime?
  expiredAt         DateTime?
  failedAt          DateTime?
  failureReason     String?

  // Mollie webhook data
  mollieCustomerId  String?
  molliePaymentMethod String?
  mollieWebhookData Json?         // Store full webhook payload for debugging

  // Metadata
  createdAt         DateTime      @default(now())
  updatedAt         DateTime      @updatedAt

  // Relations
  userId            String
  user              User          @relation(fields: [userId], references: [id], onDelete: Cascade)
  subscriptionId    String?
  subscription      Subscription? @relation(fields: [subscriptionId], references: [id], onDelete: SetNull)
  creditBatches     CreditBatch[] // Credit batches purchased with this payment

  @@map("payments")
}

model PaymentMethod {
  id                String    @id @default(cuid())
  mollieId          String    @unique // Mollie payment method ID
  type              String    // "creditcard", "ideal", etc.
  description       String?   // e.g., "**** **** **** 1234"
  isDefault         Boolean   @default(false)

  // Card details (if applicable)
  cardHolder        String?
  cardNumber        String?   // Last 4 digits only
  cardExpiryDate    String?   // MM/YY format
  cardFingerprint   String?   // Mollie card fingerprint

  // Metadata
  createdAt         DateTime  @default(now())
  updatedAt         DateTime  @updatedAt

  // Relations
  userId            String
  user              User      @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("payment_methods")
}

enum SubscriptionStatus {
  PENDING
  ACTIVE
  SUSPENDED
  CANCELLED
  COMPLETED
}

enum PaymentStatus {
  PENDING
  PAID
  CANCELLED
  EXPIRED
  FAILED
  AUTHORIZED
  REFUNDED
  PARTIALLY_REFUNDED
}

model ApiKey {
  id          String   @id @default(cuid())
  name        String   // User-friendly name for the API key
  keyHash     String   @unique // Hashed version of the API key
  keyPrefix   String   // First 8 characters for display (e.g., "ak_12345...")
  lastUsedAt  DateTime?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  // Scoping system
  scopes      Json     @default("[\"*\"]")
  description String?  // Optional description for the API key scope

  // Relations
  userId      String
  user        User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("api_keys")
}

// Credit system models for purchased email credits with expiration
model CreditBatch {
  id                String   @id @default(cuid())
  userId            String
  amount            Int      // Original amount purchased
  remainingAmount   Int      // Amount left to use
  purchasedAt       DateTime @default(now())
  expiresAt         DateTime // 180 days from purchase
  paymentId         String?  // Reference to payment that created this batch
  isExpired         Boolean  @default(false)

  // Extension tracking for when user purchases more credits
  originalExpiresAt DateTime? // Track if expiration was extended
  extensionCount    Int      @default(0)

  // Metadata
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt

  // Relations
  user              User               @relation(fields: [userId], references: [id], onDelete: Cascade)
  payment           Payment?           @relation(fields: [paymentId], references: [id], onDelete: SetNull)
  transactions      CreditTransaction[]

  // Indexes for performance
  @@index([userId])
  @@index([expiresAt])
  @@index([isExpired])
  @@map("credit_batches")
}

model CreditTransaction {
  id          String   @id @default(cuid())
  userId      String
  batchId     String?  // Which batch this transaction affects
  type        String   // 'purchase', 'usage', 'expiration', 'extension'
  amount      Int      // positive for purchase/refund, negative for usage
  description String?
  createdAt   DateTime @default(now())

  // Relations
  user        User         @relation(fields: [userId], references: [id], onDelete: Cascade)
  batch       CreditBatch? @relation(fields: [batchId], references: [id], onDelete: SetNull)

  // Indexes for performance
  @@index([userId])
  @@index([batchId])
  @@index([type])
  @@map("credit_transactions")
}
